@keyframes fadeInDown {
  from {
    transform: translate3d(0, -15px, 0)
  }

  to {
    transform: translate3d(0, 0, 0);
    opacity: 1
  }
}

@-webkit-keyframes fadeInDown {
  from {
    transform: translate3d(0, -15px, 0)
  }

  to {
    transform: translate3d(0, 0, 0);
    opacity: 1
  }
}

.animated {
  animation-duration: 400ms;
  animation-fill-mode: both;
}

.animatedfadeInDown {
  opacity: 0
}

.fadeInDown {
  opacity: 0;
  animation-name: fadeInDown;
  -webkit-animation-name: fadeInDown;
}

// toRightFromLeft
@keyframes toRightFromLeft {
  49% {
    transform: translate(100%);
  }
  50% {
    opacity: 0;
    transform: translate(-100%);
  }
  51% {
    opacity: 1;
  }
}

// toRightFromLeft
@keyframes toTopFromBottom {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1 !important;
    transform: translateX(0);
  }
}

@keyframes delayKeyframe {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}


@keyframes rubberBand {
  from {
    transform: scale3d(1, 1, 1);
  }

  30% {
    transform: scale3d(1.25, 0.75, 1);
  }

  40% {
    transform: scale3d(0.75, 1.25, 1);
  }

  50% {
    transform: scale3d(1.15, 0.85, 1);
  }

  65% {
    transform: scale3d(0.95, 1.05, 1);
  }

  75% {
    transform: scale3d(1.05, 0.95, 1);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}

.rubberBand {
  animation-name: rubberBand;
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-iteration-count: 1;
}

/* Gaming Background */
#background-image {
  background-color: #0a0a0f; /* Fallback color */
}

#particles-background {
  background-color: transparent; /* Transparent background for particles */
}

body {
  position: relative; /* Ensure the background stays behind content */
}

/* Add a little subtle glow effect to elements on top of particles */
.app-inner {
  position: relative;
  z-index: 1;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  width: 100%; /* Ensure it doesn't exceed parent width */

  /* Hide scrollbar but allow scrolling */
  &::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari and Opera */
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Enhanced glow effects for gaming theme elements */
.gaming-theme {
  /* Neon text effects for headings */
  h1, h2, h3, .s-block__title h2 {
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.4),
                 0 0 20px rgba(0, 255, 255, 0.2);
    letter-spacing: 0.5px;
  }

  /* Enhanced button glow */
  .btn-primary,
  .s-button-primary {
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.4);
    border-color: rgba(0, 255, 255, 0.5);
    position: relative;
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 50%;
      height: 100%;
      background: linear-gradient(
        to right,
        transparent,
        rgba(255, 0, 255, 0.2),
        transparent
      );
      transform: skewX(-25deg);
      transition: all 0.75s ease;
    }

    &:hover {
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      transform: translateY(-2px);

      &:before {
        left: 150%;
      }
    }
  }

  /* Product cards with glass morphism effect */
  .product-entry {
    background-color: rgba(20, 20, 35, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(128, 0, 255, 0.2);
    transition: all 0.3s ease;

    .product-title {
      color: #fff;
      font-weight: 600;
    }

    .product-price {
      color: rgba(0, 255, 255, 0.9);
      font-weight: bold;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(255, 0, 255, 0.3);
      border-color: rgba(0, 255, 255, 0.3);
    }
  }

  /* Add subtle grid lines to some containers */
  .container {
    position: relative;
    overflow-x: hidden; /* Prevent horizontal scrolling */

    /* Hide scrollbar but allow scrolling */
    &::-webkit-scrollbar {
      display: none; /* Hide scrollbar for Chrome, Safari and Opera */
    }
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        linear-gradient(to right, rgba(29, 233, 182, 0.05) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(29, 233, 182, 0.05) 1px, transparent 1px);
      background-size: 20px 20px;
      pointer-events: none;
      z-index: -1;
    }
  }
}