{#
| Variable                | Type           | Description |
|-------------------------|----------------|-------------|
| article.title           | string         |             |
| article.body            | string *HTML   |             |
| article.url             | string         |             |
| article.has_image       | Bool           |             |
| article.image           | object         |             |
| article.image.url       | string         |             |
| article.image.alt       | string         |             |
| article.thumbnail       | string         |             |
| article.created_at      | ?Date *Carbon  |             |
| article.author.name     | string         |             |
| article.author.url      | string         |             |
| article.related         | Article[]      |             |
| article.tags            | BlogTag[]      |             |
| article.tags[].name     | string         |             |
| article.tags[].url      | string         |             |
#}
{% extends "layouts.master" %}
{% block content %}
    <div class="container mb-8 sm:mb-24">
        {# add breadcumbs container in pages to make a space in case breadcrumbs is off #}
        <nav class="breadcrumbs w-full py-5 center-between">
            <salla-breadcrumb></salla-breadcrumb>
        </nav>
        <div class="md:flex items-start">
            <div class="main-content blog-category md:w-96 flex-1 bg-white lg:p-8 rounded-md mb-10 md:mb-0">
                <h1 class="font-bold text-3xl mb-5 leading-10">{{ article.title }}</h1>
                <div class="mb-10 text-gray-500 rounded-md inline-flex text-sm rtl:space-x-reverse space-x-5">
                    <div class=" flex items-center">
                        <i class="sicon-calendar-date rtl:ml-1 ltr:mr-1"></i>
                        <span class="">{{ article.created_at|date }}</span>
                    </div>
                    <a href="{{ article.author.url }}" class=" flex items-center hover:text-dark">
                        <i class="sicon-user rtl:ml-1 ltr:mr-1"></i>
                        <span class="">{{ article.author.name }}</span>
                    </a>
                </div>
                {% if article.has_image %}
                    <img src="{{ article.image.url }}" alt="{{ article.image.alt }}" title="{{ article.image.alt }}"
                         class="h-80 mb-10 w-full object-cover rounded-md"/>
                {% endif %}
                
                {% if store.settings.blog.allow_likes_and_comments %}
                <div class="flex items-center gap-2.5 text-gray-500 text-base mb-2">
                    <button id="blog-like" data-blog-id="{{ article.key }}" class="flex items-center gap-1">
                        <i class="sicon-thumbs-up text-primary"></i>
                        <span>{{article.likes_count}}</span>
                    </button>                    
                    {% if article.comments_count %}
                        <div class="flex items-center gap-1">
                            <i class="sicon-chat text-primary"></i>
                            {{article.comments_count}}
                        </div>
                    {% endif %}
                </div>
                {% endif %}

                <article class="leading-7 text-sm mb-10">{{ article.body|replace({'&nbsp;': ' '})|raw }}</article>
                {% if article.tags|length %}
                    <div class="p-4 flex flex-wrap items-center border-t border-gray-200">
                        {% for tag in article.tags %}
                            <a href="{{ tag.url }}"
                               class="py-2 px-4 rounded-large inline-flex items-center text-gray-500 hover:text-dark justify-center text-sm">
                                <i class="font-medium sicon-tag rtl:ml-1.5 ltr:mr-1.5"></i>
                                <span class="fix-align whitespace-nowrap">{{ tag.name }}</span>
                            </a>
                        {% endfor %}
                    </div>
                {% endif %}
                {% if store.settings.blog.allow_likes_and_comments %}
                    <salla-comments item-id="{{article.key}}"   type="blog"></salla-comments>
                {% endif %}
            </div>

            <aside aria-label="Sidebar" class="sticky shrink-0 top-24 md:w-96 rtl:lg:mr-8 ltr:lg:ml-8 pb-11">
                {% if related|length %}
                    <h2 class="font-bold mb-3">{{ trans('pages.blog_articles.related') }}</h2>

                    {% hook 'blog:single.items.start' %}

                    {% for article in related %}
                        <div class="mt-5 flex items-center bg-white p-5 rounded-md">
                            <div class="shrink-0">
                                <a href="{{ article.url }}"
                                   class="bg-border-color h-24 w-24 rounded-md overflow-hidden flex items-center justify-center">
                                    <span class="sr-only">{{ article.name }}</span>

                                    {% if article.has_image %}
                                        <img class="h-full w-full object-cover" src="{{ article.image.url }}"
                                             alt="{{ article.image.alt }}">
                                    {% else %}
                                        <img class="sm:h-full w-10 object-contain opacity-60"
                                             src="{{ 'images/placeholder.png' | asset }}" alt="post image"/>
                                    {% endif %}
                                </a>
                            </div>
                            <div class="rtl:mr-3 ltr:ml-3">
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="sicon-calendar-date rtl:ml-1 ltr:mr-1 rtl:pl-px ltr:pr-px"></i>
                                    <time>{{ article.created_at|date }}</time>
                                </div>

                                <h3 class="text-sm font-bold text-gray-800 leading-6">
                                    <a href="{{ article.url }}" class="hover:underline">{{ article.title }}</a>
                                </h3>
                            </div>
                        </div>
                    {% endfor %}

                    {% hook 'blog:single.items.end' %}
                {% endif %}
            </aside>
        </div>
    </div>
{% endblock %}
