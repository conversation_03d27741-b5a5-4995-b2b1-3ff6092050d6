{# مكون خط فاصل بثيم الألعاب #}
{% set component_class = component_class|default('s-block py-3') %}

<div class="{{ component_class }} gaming-advanced-divider">
    <div class="divider-line">
        <span class="divider-glow"></span>
    </div>
</div>

<style>
    /* أنماط مكون الخط الفاصل المتقدم بثيم الألعاب */
    .gaming-advanced-divider {
        width: 100%;
        display: flex;
        align-items: center;
        margin: 35px 0;
        position: relative;
    }
    
    .divider-line {
        height: 2px;
        width: 100%;
        background: linear-gradient(90deg, 
            transparent 0%, 
            rgba(29, 233, 182, 0.3) 20%, 
            rgba(29, 233, 182, 0.8) 50%, 
            rgba(29, 233, 182, 0.3) 80%, 
            transparent 100%);
        position: relative;
        overflow: hidden;
        border-radius: 2px;
    }
    
    .divider-line::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        background: linear-gradient(90deg, 
            transparent 0%, 
            rgba(29, 233, 182, 0.1) 30%, 
            rgba(29, 233, 182, 0.5) 50%, 
            rgba(29, 233, 182, 0.1) 70%, 
            transparent 100%);
        box-shadow: 0 0 15px rgba(29, 233, 182, 0.6);
        filter: blur(1px);
    }
    
    .divider-line::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 50%;
        height: 100%;
        background: linear-gradient(90deg, 
            transparent, 
            rgba(255, 255, 255, 0.8), 
            transparent);
        animation: shine 3s infinite ease-in-out;
    }
    
    .divider-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100px;
        height: 2px;
        background-color: rgba(29, 233, 182, 0.9);
        filter: blur(5px);
        box-shadow: 
            0 0 15px rgba(29, 233, 182, 0.8),
            0 0 30px rgba(29, 233, 182, 0.6),
            0 0 45px rgba(29, 233, 182, 0.4);
        border-radius: 50%;
        opacity: 0.8;
        animation: pulse 4s infinite alternate ease-in-out;
    }
    
    @keyframes shine {
        0% { left: -100%; }
        100% { left: 200%; }
    }
    
    @keyframes pulse {
        0% {
            opacity: 0.5;
            width: 50px;
            filter: blur(3px);
        }
        50% {
            opacity: 0.8;
            width: 150px;
            filter: blur(6px);
        }
        100% {
            opacity: 0.5;
            width: 50px;
            filter: blur(3px);
        }
    }
    
    /* Support for RTL layouts */
    [dir="rtl"] .divider-line {
        background: linear-gradient(270deg, 
            transparent 0%, 
            rgba(29, 233, 182, 0.3) 20%, 
            rgba(29, 233, 182, 0.8) 50%, 
            rgba(29, 233, 182, 0.3) 80%, 
            transparent 100%);
    }
    
    [dir="rtl"] .divider-line::before {
        background: linear-gradient(270deg, 
            transparent 0%, 
            rgba(29, 233, 182, 0.1) 30%, 
            rgba(29, 233, 182, 0.5) 50%, 
            rgba(29, 233, 182, 0.1) 70%, 
            transparent 100%);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .gaming-advanced-divider {
            margin: 25px 0;
        }
        
        .divider-glow {
            width: 80px;
        }
        
        @keyframes pulse {
            0% {
                opacity: 0.5;
                width: 40px;
            }
            50% {
                opacity: 0.8;
                width: 120px;
            }
            100% {
                opacity: 0.5;
                width: 40px;
            }
        }
    }
    
    @media (max-width: 480px) {
        .gaming-advanced-divider {
            margin: 20px 0;
        }
        
        .divider-line {
            height: 1.5px;
        }
        
        .divider-glow {
            width: 60px;
            height: 1.5px;
        }
        
        @keyframes pulse {
            0% {
                width: 30px;
            }
            50% {
                width: 80px;
            }
            100% {
                width: 30px;
            }
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Advanced gaming divider initialized');
    });
</script>