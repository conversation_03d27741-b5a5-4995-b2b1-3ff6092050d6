<!-- To eliminate all render blocking CSS & JS and start with the critical css-->
<!-- To show loader to the user till the page content are loaded-->
<style>
  .loader-init {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #0a0e12;
      transition: opacity 0.75s, visibility 0.75s;
      z-index: 9999;
      overflow: hidden;
  }

  .loader-init--hidden {
      opacity: 0;
      visibility: hidden;
  }

  .loader-init::after {
      content: "";
      width: 36px;
      height: 36px;
      border: 3px solid rgba(29, 233, 182, 0.2);
      border-top-color: #1DE9B6;
      border-radius: 50%;
      box-shadow: 0 0 15px rgba(29, 233, 182, 0.5);
      animation: loading 0.75s ease infinite;
  }
  
  .loader-init::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 304 304' width='304' height='304'%3E%3Cpath fill='%231de9b6' fill-opacity='0.05' d='M44.1 224a5 5 0 1 1 0 2H0v-2h44.1zm160 48a5 5 0 1 1 0 2H82v-2h122.1zm57.8-46a5 5 0 1 1 0-2H304v2h-42.1zm0 16a5 5 0 1 1 0-2H304v2h-42.1zm6.2-114a5 5 0 1 1 0 2h-86.2a5 5 0 1 1 0-2h86.2zm-256-48a5 5 0 1 1 0 2H0v-2h12.1zm185.8 34a5 5 0 1 1 0-2h86.2a5 5 0 1 1 0 2h-86.2zM258 12.1a5 5 0 1 1-2 0V0h2v12.1zm-64 208a5 5 0 1 1-2 0v-54.2a5 5 0 1 1 2 0v54.2zm48-198.2V80h62v2h-64V21.9a5 5 0 1 1 2 0zm16 16V64h46v2h-48V37.9a5 5 0 1 1 2 0zm-128 96V208h16v12.1a5 5 0 1 1-2 0V210h-16v-76.1a5 5 0 1 1 2 0zm-5.9-21.9a5 5 0 1 1 0 2H114v48H85.9a5 5 0 1 1 0-2H112v-48h12.1zm-6.2 130a5 5 0 1 1 0-2H176v-74.1a5 5 0 1 1 2 0V242h-60.1zm-16-64a5 5 0 1 1 0-2H114v48h10.1a5 5 0 1 1 0 2H112v-48h-10.1zM66 284.1a5 5 0 1 1-2 0V274H50v30h-2v-32h18v12.1zM236.1 176a5 5 0 1 1 0 2H226v94h48v32h-2v-30h-48v-98h12.1zm25.8-30a5 5 0 1 1 0-2H274v44.1a5 5 0 1 1-2 0V146h-10.1zm-64 96a5 5 0 1 1 0-2H208v-80h16v-14h-42.1a5 5 0 1 1 0-2H226v18h-16v80h-12.1zm86.2-210a5 5 0 1 1 0 2H272V0h2v32h10.1zM98 101.9V146H53.9a5 5 0 1 1 0-2H96v-42.1a5 5 0 1 1 2 0zM53.9 34a5 5 0 1 1 0-2H80V0h2v34H53.9zm60.1 3.9V66H82v64H69.9a5 5 0 1 1 0-2H80V64h32V37.9a5 5 0 1 1 2 0zM101.9 82a5 5 0 1 1 0-2H128V37.9a5 5 0 1 1 2 0V82h-28.1zm16-64a5 5 0 1 1 0-2H146v44.1a5 5 0 1 1-2 0V18h-26.1z'%3E%3C/path%3E%3C/svg%3E");
      opacity: 0.15;
      z-index: -1;
  }

  @keyframes loading {
      from {
        transform: rotate(0turn);
      }
      to {
        transform: rotate(1turn);
      }
  }
  
  /* Glow effects */
  .loader-init::after {
      position: relative;
      z-index: 2;
  }
  
  .loader-glow {
      position: absolute;
      width: 100px;
      height: 100px;
      background: radial-gradient(circle, rgba(29, 233, 182, 0.3) 0%, rgba(29, 233, 182, 0) 70%);
      border-radius: 50%;
      z-index: 1;
      animation: pulse 2s infinite ease-in-out;
  }
  
  @keyframes pulse {
      0% { opacity: 0.5; transform: scale(0.8); }
      50% { opacity: 0.9; transform: scale(1.2); }
      100% { opacity: 0.5; transform: scale(0.8); }
  }
</style>
<script>
  window.addEventListener("DOMContentLoaded", () => {
    const loader = document.querySelector(".loader-init");
    
    // Add the glow effect
    if (loader) {
      const glow = document.createElement('div');
      glow.className = 'loader-glow';
      loader.appendChild(glow);
    }
  });
  
  window.addEventListener("load", () => {
    const loader = document.querySelector(".loader-init");
    if (!loader) return;
    
    loader.classList.add("loader-init--hidden");
    loader.addEventListener("transitionend", () => {
      if (!document.querySelector(".loader-init")) return;
      document.body.removeChild(loader);
    });
  });
</script>