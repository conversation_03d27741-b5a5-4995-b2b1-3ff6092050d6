{#
| Variable                | Type     | Description                                                                  |
|-------------------------|----------|------------------------------------------------------------------------------|
| component               | object   | Contains merchant settings for fields from twilight.json `component` section |
| component.video_url     | string   | Video URL (YouTube, Vimeo, or direct video link)                            |
| component.title         | string   | Main title to display on video                                              |
| component.subtitle      | string   | Subtitle/description to display under main title                            |
| position                | int      | Component position for unique IDs                                           |
#}

{% set video_url = component.video_url %}
{% set title = component.title %}
{% set subtitle = component.subtitle %}
{% set unique_id = 'video-banner-' ~ position %}

{# Extract video ID and determine video type #}
{% set video_id = null %}
{% set video_type = 'direct' %}
{% set embed_url = video_url %}

{# YouTube URL processing #}
{% if video_url and 'youtube.com/watch' in video_url %}
    {% set video_id = video_url|split('v=')|last|split('&')|first %}
    {% set video_type = 'youtube' %}
    {% set embed_url = 'https://www.youtube.com/embed/' ~ video_id ~ '?autoplay=1&mute=1&loop=1&playlist=' ~ video_id ~ '&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1' %}
{% elseif video_url and 'youtu.be/' in video_url %}
    {% set video_id = video_url|split('youtu.be/')|last|split('?')|first %}
    {% set video_type = 'youtube' %}
    {% set embed_url = 'https://www.youtube.com/embed/' ~ video_id ~ '?autoplay=1&mute=1&loop=1&playlist=' ~ video_id ~ '&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1' %}
{# Vimeo URL processing #}
{% elseif video_url and 'vimeo.com/' in video_url %}
    {% set video_id = video_url|split('vimeo.com/')|last|split('?')|first %}
    {% set video_type = 'vimeo' %}
    {% set embed_url = 'https://player.vimeo.com/video/' ~ video_id ~ '?autoplay=1&muted=1&loop=1&background=1&controls=0' %}
{% endif %}

<section class="s-block s-block--video-banner video-banner-section" id="{{ unique_id }}">
    {% if video_url %}
        <div class="video-banner-container">
            {# Video Background #}
            <div class="video-background">
                {% if video_type == 'direct' %}
                    <video autoplay muted loop playsinline class="video-element">
                        <source src="{{ video_url }}" type="video/mp4">
                        <p>متصفحك لا يدعم عرض الفيديو.</p>
                    </video>
                {% else %}
                    <iframe
                        src="{{ embed_url }}"
                        class="video-iframe"
                        frameborder="0"
                        allow="autoplay; fullscreen; picture-in-picture"
                        allowfullscreen>
                    </iframe>
                {% endif %}
            </div>

            {# Overlay #}
            <div class="video-overlay"></div>

            {# Content #}
            {% if title or subtitle %}
                <div class="video-content">
                    <div class="container">
                        <div class="video-text-content">
                            {% if title %}
                                <h1 class="video-title animate-element">{{ title }}</h1>
                            {% endif %}

                            {% if subtitle %}
                                <p class="video-subtitle animate-element">{{ subtitle }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    {% else %}
        {# Fallback when no video URL is provided #}
        <div class="video-banner-placeholder">
            <div class="container">
                <div class="placeholder-content">
                    <i class="sicon-video placeholder-icon"></i>
                    <h3>بنر فيديو</h3>
                    <p>يرجى إضافة رابط الفيديو من إعدادات الكومبوننت</p>
                </div>
            </div>
        </div>
    {% endif %}
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const videoBanner = document.getElementById('{{ unique_id }}');
    if (!videoBanner) return;

    // Intersection Observer for animation when element comes into view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Animate text elements with delay
                const animateElements = entry.target.querySelectorAll('.animate-element');
                animateElements.forEach((element, index) => {
                    element.style.transitionDelay = `${0.5 + (index * 0.3)}s`;
                    element.classList.add('animate-in');
                });

                // Unobserve after animation is triggered
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.3 // Trigger when 30% of the element is visible
    });

    observer.observe(videoBanner);

    // Handle video loading and error states
    const videoElement = videoBanner.querySelector('.video-element');
    const videoIframe = videoBanner.querySelector('.video-iframe');

    if (videoElement) {
        videoElement.addEventListener('loadeddata', function() {
            videoBanner.classList.add('video-loaded');
        });

        videoElement.addEventListener('error', function() {
            console.warn('Video failed to load:', '{{ video_url }}');
            videoBanner.classList.add('video-error');
        });
    }

    if (videoIframe) {
        videoIframe.addEventListener('load', function() {
            videoBanner.classList.add('video-loaded');
        });
    }

    // Pause video when not in viewport (performance optimization)
    const pauseObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const video = entry.target.querySelector('.video-element');
            if (video) {
                if (entry.isIntersecting) {
                    video.play().catch(e => console.log('Video autoplay prevented'));
                } else {
                    video.pause();
                }
            }
        });
    }, {
        threshold: 0.1
    });

    pauseObserver.observe(videoBanner);
});
</script>

<style>
/* Video Banner Component Styles */
.video-banner-section {
    position: relative;
    width: 100%;
    margin-bottom: 2rem;
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.video-banner-section.animate-in {
    opacity: 1;
    transform: scale(1);
}

.video-banner-container {
    position: relative;
    width: 100%;
    height: 70vh;
    min-height: 500px;
    max-height: 800px;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Video Background */
.video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.video-element,
.video-iframe {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    min-width: 100%;
    min-height: 100%;
    transform: translate(-50%, -50%);
    object-fit: cover;
    border: none;
    outline: none;
}

/* Video Overlay */
.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.4) 0%,
        rgba(0, 0, 0, 0.2) 50%,
        rgba(0, 0, 0, 0.6) 100%
    );
    z-index: 2;
}

/* Content */
.video-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    padding: 2rem;
}

.video-text-content {
    text-align: center;
    color: #fff;
    max-width: 800px;
}

/* Text Animation Elements */
.animate-element {
    opacity: 0;
    transform: translateY(40px);
    transition: opacity 1s ease, transform 1s ease;
    transition-delay: 0s;
}

.animate-element.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Title Styles */
.video-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.8);
    line-height: 1.2;
    position: relative;
    display: inline-block;
}

.video-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 4px;
    background: var(--color-primary, #1DE9B6);
    box-shadow: 0 0 15px var(--color-primary, #1DE9B6);
    transition: width 1s ease 1.2s;
}

.video-title.animate-in::after {
    width: 80px;
}

/* Subtitle Styles */
.video-subtitle {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.6;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
    opacity: 0.95;
    max-width: 600px;
    margin: 0 auto;
}

/* Placeholder Styles */
.video-banner-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.placeholder-content {
    text-align: center;
    color: #6c757d;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.placeholder-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #495057;
}

.placeholder-content p {
    font-size: 1rem;
    opacity: 0.8;
}

/* Loading and Error States */
.video-banner-section:not(.video-loaded) .video-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-banner-section:not(.video-loaded) .video-background::after {
    content: 'جاري تحميل الفيديو...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 1.2rem;
    z-index: 11;
}

.video-banner-section.video-error .video-background::after {
    content: 'فشل في تحميل الفيديو';
    color: #ff6b6b;
}

/* Responsive Design */
@media (max-width: 768px) {
    .video-banner-container {
        height: 60vh;
        min-height: 400px;
        border-radius: 8px;
    }

    .video-content {
        padding: 1rem;
    }

    .video-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .video-subtitle {
        font-size: 1.1rem;
    }

    .video-title::after {
        width: 60px;
        height: 3px;
    }
}

@media (max-width: 480px) {
    .video-banner-container {
        height: 50vh;
        min-height: 350px;
    }

    .video-title {
        font-size: 2rem;
    }

    .video-subtitle {
        font-size: 1rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .video-banner-section,
    .animate-element,
    .video-title::after {
        transition: none;
        animation: none;
    }

    .video-banner-section {
        opacity: 1;
        transform: none;
    }

    .animate-element {
        opacity: 1;
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .video-overlay {
        background: rgba(0, 0, 0, 0.8);
    }

    .video-title,
    .video-subtitle {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
    }
}
</style>