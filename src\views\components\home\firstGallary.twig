{# معرض مع صور مصغرة - ديناميكي #}
{% set component_class = component_class|default('s-block py-0') %}
{% set title = component.title %}
{% set description = component.description %}
{% set banner = component.banner %}
{% set icon_items = component.icon %}

<section class="{{ component_class }} gaming-gallery-showcase">
    <div class="full-width-banner">
        <div class="game-gallery-banner" 
            {% if banner %}style="background-image: url('{{ banner }}')"{% endif %}>
            
            <div class="game-gallery-content">
                {% if title %}
                    <div class="game-gallery-title">
                        <h2>{{ title }}</h2>
                    </div>
                {% endif %}
                
                {% if description %}
                    <div class="game-gallery-description">
                        <p>{{ description }}</p>
                    </div>
                {% endif %}
                
                {% if component.button_text %}
                    <div class="game-gallery-button">
                        <a href="{{ component.button_url|default('#') }}" class="game-cta-button">
                            {{ component.button_text }}
                        </a>
                    </div>
                {% endif %}
            </div>
            
            {% if icon_items|length > 0 %}
                <div class="game-gallery-icons">
                    <div class="game-cards-container">
                        {% for icon in icon_items %}
                            <div class="game-card">
                                <div class="game-gallery-icon">
                                    {% if icon.image is defined and icon.image %}
                                        <img src="{{ icon.image }}" alt="Gallery Icon" class="img-fluid">
                                    {% elseif icon.img is defined and icon.img %}
                                        <img src="{{ icon.img }}" alt="Gallery Icon" class="img-fluid">
                                    {% elseif icon['icon.image'] is defined and icon['icon.image'] %}
                                        <img src="{{ icon['icon.image'] }}" alt="Gallery Icon" class="img-fluid">
                                    {% elseif attribute(icon, 'image') is defined %}
                                        <img src="{{ attribute(icon, 'image') }}" alt="Gallery Icon" class="img-fluid">
                                    {% else %}
                                        <div class="icon-placeholder">Icon</div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
            
            <div class="gaming-particle-overlay"></div>
        </div>
    </div>
</section>

<style>
    /* أنماط مكون معرض الصور بثيم الألعاب */
    .gaming-gallery-showcase {
        margin: 0;
        padding: 0;
        width: 100vw;
        position: relative;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
        overflow: hidden;
    }
    
    .full-width-banner {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .game-gallery-banner {
        position: relative;
        background-color: #0c1824;
        background-size: cover;
        background-position: center;
        overflow: hidden;
        padding: 80px 20px 140px; /* زيادة المساحة في الأسفل للأيقونات */
        min-height: 500px; /* زيادة الارتفاع لإفساح مجال للأيقونات */
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        box-shadow: 0 10px 30px -8px rgba(0, 0, 0, 0.5);
        width: 100%;
    }
    
    .game-gallery-banner::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(180deg, 
            rgba(12, 24, 36, 0.7) 0%, 
            rgba(8, 8, 24, 0.85) 100%);
        z-index: 1;
    }
    
    .game-gallery-content {
        position: relative;
        z-index: 3;
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
        padding: 40px 20px;
        background-color: rgba(8, 8, 24, 0.4);
        border-radius: 15px;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(29, 233, 182, 0.2);
        box-shadow: 0 0 30px rgba(29, 233, 182, 0.1);
    }
    
    .game-gallery-title h2 {
        color: #1DE9B6;
        font-weight: 700;
        font-size: 2.5rem;
        margin-bottom: 20px;
        text-shadow: 0 0 15px rgba(29, 233, 182, 0.7);
        letter-spacing: 0.03em;
        position: relative;
        display: inline-block;
    }
    
    .game-gallery-title h2::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background: linear-gradient(90deg, transparent, #1DE9B6, transparent);
        box-shadow: 0 0 10px rgba(29, 233, 182, 0.8);
    }
    
    .game-gallery-description p {
        color: #ffffff;
        font-size: 1.2rem;
        line-height: 1.7;
        margin: 25px 0;
        text-shadow: 0 0 8px rgba(0, 0, 0, 0.8);
    }
    
    .game-cta-button {
        display: inline-block;
        background: linear-gradient(90deg, #00BFA5, #1DE9B6);
        border: none;
        padding: 12px 30px;
        border-radius: 50px;
        color: #0c1824;
        font-weight: 600;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        box-shadow: 0 5px 20px rgba(29, 233, 182, 0.5);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    .game-cta-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, #1DE9B6, #4CC9F0);
        z-index: -1;
        transition: transform 0.6s cubic-bezier(0.65, 0, 0.35, 1);
        transform: scaleX(0);
        transform-origin: right;
    }
    
    .game-cta-button:hover::before {
        transform: scaleX(1);
        transform-origin: left;
    }
    
    .game-cta-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(29, 233, 182, 0.7);
        color: #0c1824;
        text-decoration: none;
    }
    
    /* تصميم بطاقات الأيقونات */
    .game-gallery-icons {
        position: absolute;
        bottom: 40px;
        left: 0;
        right: 0;
        z-index: 3;
        width: 100%;
        text-align: center;
    }
    
    .game-cards-container {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }
    
    .game-card {
        flex: 0 0 150px;
        max-width: 150px;
        perspective: 1000px;
        margin-bottom: 10px;
        transform-style: preserve-3d;
        transition: transform 0.5s ease;
    }
    
    .game-gallery-icon {
        background-color: rgba(26, 46, 63, 0.8);
        border: 2px solid #1DE9B6;
        border-radius: 12px;
        padding: 20px;
        height: 100%;
        min-height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.4s ease;
        transform: translateZ(0);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(29, 233, 182, 0.3);
        opacity: 1;
        width: 100%;
        position: relative;
        overflow: hidden;
    }
    
    .game-gallery-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(29, 233, 182, 0.2) 0%, transparent 80%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .game-gallery-icon:hover::before {
        opacity: 1;
    }
    
    .game-card:hover {
        transform: rotateY(10deg) rotateX(10deg);
    }
    
    .game-gallery-icon:hover {
        transform: translateY(-8px) translateZ(20px);
        box-shadow: 0 15px 30px rgba(29, 233, 182, 0.5), 0 0 15px rgba(29, 233, 182, 0.5);
        border-color: #4CC9F0;
    }
    
    /* Placeholder للأيقونات */
    .icon-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #1DE9B6;
        font-weight: bold;
        text-shadow: 0 0 5px rgba(29, 233, 182, 0.7);
        font-size: 0.9rem;
    }
    
    .game-gallery-icon img {
        max-width: 100%;
        max-height: 60px;
        object-fit: contain;
        display: block;
        filter: drop-shadow(0 0 5px rgba(29, 233, 182, 0.5));
        transition: transform 0.3s ease, filter 0.3s ease;
    }
    
    .game-gallery-icon:hover img {
        transform: scale(1.1);
        filter: drop-shadow(0 0 10px rgba(29, 233, 182, 0.8));
    }
    
    /* تأثير الحدود المتوهجة */
    .game-gallery-banner::after {
        content: '';
        position: absolute;
        inset: -1px;
        background: linear-gradient(130deg, 
            rgba(0, 191, 165, 0) 20%, 
            rgba(29, 233, 182, 0.1) 30%, 
            rgba(29, 233, 182, 0.1) 50%, 
            rgba(0, 191, 165, 0.1) 70%, 
            rgba(29, 233, 182, 0) 80%);
        border-radius: 0;
        pointer-events: none;
        z-index: 2;
        background-size: 300% 300%;
        animation: border-glow 3s linear infinite;
    }
    
    /* تأثير الجزيئات المتحركة */
    .gaming-particle-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        background-image: 
            radial-gradient(circle at 25% 25%, rgba(29, 233, 182, 0.05) 0%, transparent 20%),
            radial-gradient(circle at 75% 50%, rgba(76, 201, 240, 0.05) 0%, transparent 20%),
            radial-gradient(circle at 50% 80%, rgba(123, 97, 255, 0.05) 0%, transparent 20%);
        background-size: 200% 200%, 150% 150%, 250% 250%;
        animation: particle-shift 20s ease infinite;
        opacity: 0.6;
        pointer-events: none;
    }
    
    @keyframes particle-shift {
        0%, 100% { background-position: 0% 0%, 0% 0%, 0% 0%; }
        25% { background-position: 50% 25%, 25% 75%, 75% 50%; }
        50% { background-position: 100% 50%, 50% 100%, 100% 25%; }
        75% { background-position: 50% 75%, 75% 25%, 25% 100%; }
    }
    
    @keyframes border-glow {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    @media (max-width: 768px) {
        .game-gallery-title h2 {
            font-size: 1.8rem;
        }
        
        .game-gallery-description p {
            font-size: 1rem;
        }
        
        .game-gallery-banner {
            padding: 60px 15px 120px;
        }
        
        .game-card {
            flex: 0 0 120px;
            max-width: 120px;
        }
        
        .game-gallery-icon {
            padding: 15px;
            min-height: 80px;
        }
        
        .game-gallery-content {
            padding: 30px 15px;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعديل حجم البنر ديناميكيًا إذا كان هناك عدد كبير من الأيقونات
        const iconsContainer = document.querySelector('.game-gallery-icons');
        const banner = document.querySelector('.game-gallery-banner');
        const galleryIcons = document.querySelectorAll('.game-gallery-icon');
        
        console.log('Gallery icons found:', galleryIcons.length);
        
        // فحص كل صورة ومعالجة الأخطاء
        galleryIcons.forEach((icon, index) => {
            const img = icon.querySelector('img');
            const placeholder = icon.querySelector('.icon-placeholder');
            
            // طباعة معلومات حول كل عنصر
            console.log(`Icon ${index + 1}:`, {
                hasImage: !!img,
                hasPlaceholder: !!placeholder,
                element: icon.innerHTML
            });
            
            if (img) {
                // تسجيل معلومات الصورة
                console.log(`Icon ${index + 1} image:`, img.getAttribute('src'));
                
                // التأكد من أن الصورة مرئية
                img.style.display = 'block';
                img.style.visibility = 'visible';
                img.style.opacity = '1';
                
                // معالجة أخطاء تحميل الصورة
                img.onerror = function() {
                    console.error(`Failed to load icon ${index + 1} image:`, img.getAttribute('src'));
                    // إنشاء عنصر بديل في حالة عدم تحميل الصورة
                    icon.innerHTML = '<div class="icon-placeholder">Image Error</div>';
                };
                
                img.onload = function() {
                    console.log(`Icon ${index + 1} image loaded successfully`);
                };
            }
            
            // إضافة تأثيرات التحويم والدوران ثلاثي الأبعاد
            icon.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) translateZ(20px)';
                this.style.boxShadow = '0 15px 30px rgba(29, 233, 182, 0.6), 0 0 15px rgba(29, 233, 182, 0.6)';
                this.style.borderColor = '#4CC9F0';
                
                const parentCard = this.closest('.game-card');
                if (parentCard) {
                    parentCard.style.transform = 'rotateY(10deg) rotateX(10deg)';
                }
                
                const img = this.querySelector('img');
                if (img) {
                    img.style.transform = 'scale(1.1)';
                    img.style.filter = 'drop-shadow(0 0 10px rgba(29, 233, 182, 0.8))';
                }
            });
            
            icon.addEventListener('mouseleave', function() {
                this.style.transform = 'translateZ(0)';
                this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(29, 233, 182, 0.3)';
                this.style.borderColor = '#1DE9B6';
                
                const parentCard = this.closest('.game-card');
                if (parentCard) {
                    parentCard.style.transform = '';
                }
                
                const img = this.querySelector('img');
                if (img) {
                    img.style.transform = '';
                    img.style.filter = 'drop-shadow(0 0 5px rgba(29, 233, 182, 0.5))';
                }
            });
        });
        
        // تعديل ارتفاع البنر لاستيعاب الأيقونات
        if (iconsContainer && banner && galleryIcons.length > 0) {
            // زيادة المساحة في الأسفل إذا كان هناك عدد كبير من الأيقونات
            if (galleryIcons.length > 6) {
                banner.style.paddingBottom = '180px';
            }
            
            // ضبط موضع الأيقونات في أسفل البنر
            const adjustIconsPosition = function() {
                const bannerHeight = banner.offsetHeight;
                const contentHeight = document.querySelector('.game-gallery-content').offsetHeight;
                const iconsHeight = iconsContainer.offsetHeight;
                
                console.log('Banner dimensions:', {
                    bannerHeight: bannerHeight,
                    contentHeight: contentHeight,
                    iconsHeight: iconsHeight
                });
                
                // التأكد من وجود مساحة كافية للأيقونات
                if (bannerHeight < contentHeight + iconsHeight + 80) {
                    banner.style.minHeight = (contentHeight + iconsHeight + 120) + 'px';
                }
            };
            
            // تنفيذ ضبط الموقع بعد تحميل الصفحة وعند تغيير حجم النافذة
            setTimeout(adjustIconsPosition, 100); // تأخير قليل للتأكد من تحميل الصور
            window.addEventListener('resize', adjustIconsPosition);
        }
        
        console.log('Enhanced Gallery component fully initialized');
        document.body.classList.add('game-gallery-loaded');
    });
</script>