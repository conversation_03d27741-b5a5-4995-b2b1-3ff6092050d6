import classCallCheck from '@babel/runtime/helpers/classCallCheck';
import createClass from '@babel/runtime/helpers/createClass';
import wrapNativeSuper from '@babel/runtime/helpers/wrapNativeSuper';

/**
 * مكون فلترة حسب الفئات
 * يعرض أيقونات الفئات في الهيدر ويظهر المنتجات لكل فئة عند النقر عليها
 */
class CategoryFilter extends wrapNativeSuper(HTMLElement) {
  constructor() {
    super();
    classCallCheck(this, CategoryFilter);
    this.categories = [];
    this.products = {};
    this.activeCategory = null;
    
    // تأخير التهيئة حتى تحميل سلة
    if (window.salla) {
      this.initComponent();
    } else {
      document.addEventListener('salla::ready', () => this.initComponent());
    }
  }

  /**
   * تهيئة المكون
   */
  initComponent() {
    // إضافة الأنماط مباشرة للتأكد من عملها
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      .category-filter-container {
        display: flex;
        flex-wrap: nowrap;
        gap: 0.75rem;
        align-items: center;
        margin: 0.5rem 0;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        padding: 0.5rem 0;
        scroll-behavior: smooth;
      }
      .category-filter-container::-webkit-scrollbar {
        display: none;
      }
      .category-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0.5rem;
        border-radius: 0.75rem;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        cursor: pointer;
        transition: all 0.3s;
        min-width: 80px;
        text-align: center;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        flex: 0 0 auto;
      }
      .category-icon:hover {
        background-color: #f3f4f6;
        border-color: rgba(var(--primary-color), 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }
      .category-icon.active {
        background-color: rgba(var(--primary-color), 0.1);
        border-color: rgba(var(--primary-color), 0.4);
        transform: translateY(-2px);
        box-shadow: 0 4px 10px -2px rgba(var(--primary-color), 0.2);
      }
      .category-icon-image {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.5rem;
        background-color: #fff;
        border-radius: 50%;
        padding: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      }
      .category-icon-image img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
      .category-icon-name {
        font-size: 0.8rem;
        color: #4b5563;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: color 0.3s;
      }
      .category-icon:hover .category-icon-name,
      .category-icon.active .category-icon-name {
        color: rgba(var(--primary-color), 1);
        font-weight: 500;
      }
      .category-products {
        display: flex;
        flex-direction: column;
        margin-top: 1rem;
        overflow: hidden;
        max-height: 0;
        transition: max-height 0.4s ease-out, opacity 0.3s ease-out;
        opacity: 0;
        border-radius: 0.75rem;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
      }
      .category-products.active {
        max-height: 2000px;
        opacity: 1;
        transition: max-height 0.6s ease-in, opacity 0.4s ease-in 0.1s;
      }
      
      /* تحسين تصميم المنتجات */
      .category-products-header {
        padding: 1rem 1rem 0.5rem;
        border-bottom: 1px solid rgba(var(--primary-color), 0.1);
        text-align: center;
        background: linear-gradient(to right, rgba(var(--primary-color), 0.05), rgba(var(--primary-color), 0.1));
      }
      
      .category-products-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: rgba(var(--primary-color), 1);
        margin: 0;
      }
      
      .category-products-subtitle {
        font-size: 0.875rem;
        color: #4b5563;
        margin-top: 0.25rem;
      }
      
      .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 1rem;
        padding: 1rem;
      }
      
      .product-card {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        background-color: #fff;
        border: 1px solid #f3f4f6;
        position: relative;
      }
      .product-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        border-color: rgba(var(--primary-color), 0.2);
      }
      .product-image {
        height: 140px;
        background-color: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        position: relative;
      }
      .product-image img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        transition: transform 0.5s;
      }
      .product-card:hover .product-image img {
        transform: scale(1.05);
      }
      .product-details {
        padding: 0.75rem;
      }
      .product-name {
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #1f2937;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: color 0.3s;
      }
      .product-card:hover .product-name {
        color: rgba(var(--primary-color), 1);
      }
      .product-price {
        font-size: 0.875rem;
        color: rgba(var(--primary-color), 1);
        font-weight: 600;
      }
      
      /* مؤثرات جديدة */
      .product-actions {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: center;
        padding: 0.5rem;
        background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
        opacity: 0;
        transform: translateY(100%);
        transition: all 0.3s ease;
      }
      
      .product-card:hover .product-actions {
        opacity: 1;
        transform: translateY(0);
      }
      
      .add-to-cart-btn {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: rgba(var(--primary-color), 1);
        color: white;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      
      .add-to-cart-btn:hover {
        transform: scale(1.1);
        background-color: rgba(var(--primary-color), 0.9);
      }
      
      .view-more-btn {
        padding: 0.5rem 1.25rem;
        background-color: rgba(var(--primary-color), 1);
        color: white;
        border-radius: 0.5rem;
        text-align: center;
        transition: all 0.3s;
        font-weight: 500;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: inline-block;
      }
      
      .view-more-btn:hover {
        background-color: rgba(var(--primary-color), 0.9);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
      }
      
      /* إشعارات السلة */
      .cart-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        padding: 1rem;
        transform: translateX(120%);
        transition: transform 0.3s ease;
        max-width: 300px;
        border-left: 4px solid rgba(var(--primary-color), 1);
      }
      
      .cart-notification.show {
        transform: translateX(0);
      }
      
      .cart-notification-content {
        display: flex;
        align-items: center;
      }
      
      .cart-notification-icon {
        font-size: 1.5rem;
        color: #10b981;
        margin-right: 0.75rem;
      }
      
      .cart-notification-text p {
        margin: 0 0 0.25rem 0;
        color: #4b5563;
      }
      
      .cart-notification-text strong {
        color: #1f2937;
        font-size: 0.9rem;
        word-break: break-word;
      }
      
      .loading-spinner {
        display: inline-block;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        border: 2px solid rgba(var(--primary-color), 0.3);
        border-top-color: rgba(var(--primary-color), 1);
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      .loading-container {
        display: flex;
        justify-content: center;
        padding: 1.5rem;
      }
      .category-filter-wrapper {
        position: relative;
      }
      .category-scroll-buttons {
        display: none;
      }
      @media (min-width: 640px) {
        .category-scroll-buttons {
          display: block;
          position: absolute;
          top: 0;
          bottom: 0;
          width: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(90deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.9) 70%, rgba(255,255,255,0) 100%);
          z-index: 1;
          opacity: 0;
          transition: opacity 0.2s;
          cursor: pointer;
        }
        .category-scroll-buttons.show {
          opacity: 1;
        }
        .category-scroll-buttons.left {
          left: 0;
        }
        .category-scroll-buttons.right {
          right: 0;
          transform: rotate(180deg);
          background: linear-gradient(270deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.9) 70%, rgba(255,255,255,0) 100%);
        }
        .category-scroll-buttons i {
          font-size: 1.5rem;
          color: rgba(var(--primary-color), 0.8);
        }
      }
      
      /* أنماط جديدة للمنتجات العامة */
      .generic-product {
        opacity: 0.8;
        border: 1px dashed #e5e7eb;
        position: relative;
      }
      .generic-product::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background-color: #f3f4f6;
        z-index: 1;
      }
      .generic-product:hover {
        opacity: 1;
      }
      .generic-product .product-name {
        font-style: italic;
      }
      
      /* تحسين رسالة المنتجات العامة */
      .col-span-full {
        grid-column: 1 / -1;
        width: 100%;
      }
      
      /* تحسينات للموبايل */
      @media (max-width: 640px) {
        .products-grid {
          grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
          gap: 0.75rem;
          padding: 0.75rem;
        }
        
        .product-image {
          height: 110px;
        }
        
        .category-products-title {
          font-size: 1.1rem;
        }
        
        .category-products-subtitle {
          font-size: 0.8rem;
        }
      }
    `;
    this.appendChild(styleEl);

    // إنشاء هيكل المكون
    const containerDiv = document.createElement('div');
    containerDiv.className = 'category-filter-wrapper';
    containerDiv.innerHTML = `
      <div class="category-scroll-buttons left">
        <i class="sicon-keyboard-arrow-left"></i>
      </div>
      <div class="category-filter-container" dir="${document.dir || 'rtl'}"></div>
      <div class="category-scroll-buttons right">
        <i class="sicon-keyboard-arrow-right"></i>
      </div>
      <div class="category-products-container"></div>
    `;
    this.appendChild(containerDiv);

    // الحصول على العناصر
    this.filterContainer = this.querySelector('.category-filter-container');
    this.productsContainer = this.querySelector('.category-products-container');
    this.scrollLeftBtn = this.querySelector('.category-scroll-buttons.left');
    this.scrollRightBtn = this.querySelector('.category-scroll-buttons.right');

    // إضافة مستمعات أزرار التمرير
    this.scrollLeftBtn.addEventListener('click', this.scrollCategories.bind(this, 'left'));
    this.scrollRightBtn.addEventListener('click', this.scrollCategories.bind(this, 'right'));

    // مراقبة وضع التمرير لإظهار/إخفاء أزرار التمرير
    this.filterContainer.addEventListener('scroll', this.updateScrollButtons.bind(this));
    window.addEventListener('resize', this.updateScrollButtons.bind(this));

    // جلب الفئات
    this.loadCategories();
    
    console.log('تم تهيئة مكون فلترة الفئات');
  }

  scrollCategories(direction) {
    const container = this.filterContainer;
    const scrollAmount = container.clientWidth * 0.75;
    
    if (direction === 'left') {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  }

  updateScrollButtons() {
    const container = this.filterContainer;
    
    // عرض/إخفاء زر التمرير يسار
    if (container.scrollLeft > 20) {
      this.scrollLeftBtn.classList.add('show');
    } else {
      this.scrollLeftBtn.classList.remove('show');
    }
    
    // عرض/إخفاء زر التمرير يمين
    const isScrollable = container.scrollWidth > container.clientWidth;
    const isAtEnd = container.scrollLeft + container.clientWidth >= container.scrollWidth - 20;
    
    if (isScrollable && !isAtEnd) {
      this.scrollRightBtn.classList.add('show');
    } else {
      this.scrollRightBtn.classList.remove('show');
    }
  }

  /**
   * البحث في عناصر المنتجات المتوفرة في الصفحة
   * @param {NodeList} productElements - عناصر المنتجات
   * @param {Object} category - الفئة المستهدفة
   * @param {Array} results - مصفوفة النتائج
   * @private
   */
  _searchInAvailableProducts(productElements, category, results) {
    const categoryId = String(category.id);
    const categoryName = category.name.toLowerCase();
    
    productElements.forEach(element => {
      try {
        // تحسين اختيار اسم المنتج
        const productName = element.querySelector('.product-title, .product-name, h3, a, .name, .title, [itemprop="name"]')?.textContent?.trim() || element.textContent?.trim() || '';
        
        // الحصول على الرابط بطرق مختلفة
        let productLink = null;
        const linkElement = element.querySelector('a[href]');
        if (linkElement) {
          productLink = linkElement.href;
        } else if (element.tagName === 'A') {
          productLink = element.href;
        } else if (element.dataset.url) {
          productLink = element.dataset.url;
        } else if (element.dataset.href) {
          productLink = element.dataset.href;
        } else if (element.onclick) {
          // محاولة استخراج الرابط من وظيفة النقر
          const onClickStr = element.onclick.toString();
          const urlMatch = onClickStr.match(/(window\.location|location\.href)\s*=\s*['"]([^'"]+)['"]/);
          if (urlMatch && urlMatch[2]) {
            productLink = urlMatch[2];
          }
        }
        
        // إذا لم نجد رابطًا، تخطي هذا العنصر
        if (!productLink) return;
        
        // تحسين اختيار صورة المنتج
        let productImage = '';
        const imgElement = element.querySelector('img');
        if (imgElement) {
          productImage = imgElement.src || imgElement.dataset.src;
        } else {
          // محاولة البحث عن الصورة في الأنماط الخلفية
          const computedStyle = window.getComputedStyle(element);
          const backgroundImage = computedStyle.backgroundImage;
          if (backgroundImage && backgroundImage !== 'none') {
            const urlMatch = backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
            if (urlMatch && urlMatch[1]) {
              productImage = urlMatch[1];
            }
          }
        }
        
        // تحسين اختيار سعر المنتج
        const productPrice = element.querySelector('.product-price, .price, .s-price, [itemprop="price"], .price-wrapper')?.textContent || '';
        
        // التحقق من تطابق المنتج مع الفئة
        const isRelated = this._isProductRelatedToCategory(element, {
          name: productName,
          url: productLink
        }, categoryId, categoryName);
        
        if (isRelated) {
          results.push({
            name: productName,
            url: productLink,
            image: productImage,
            thumbnail: productImage,
            price: productPrice,
            related: true
          });
        }
      } catch (error) {
        console.error('خطأ في استخراج بيانات المنتج:', error);
      }
    });
  }

  /**
   * التحقق من تطابق المنتج مع الفئة بطريقة محسنة
   * @param {Element} element - عنصر المنتج
   * @param {Object} product - بيانات المنتج
   * @param {string} categoryId - معرف الفئة
   * @param {string} categoryName - اسم الفئة
   * @returns {boolean} - يعيد true إذا كان المنتج مرتبطًا بالفئة
   * @private
   */
  _isProductRelatedToCategory(element, product, categoryId, categoryName) {
    // فحص السمات المباشرة
    if (
      element.dataset.categoryId === categoryId || 
      element.getAttribute('data-category') === categoryId ||
      element.dataset.category === categoryId
    ) {
      return true;
    }
    
    // فحص عناصر الأب
    const parentWithCategory = element.closest(`[data-category-id="${categoryId}"], [data-category="${categoryId}"], [data-category-slug="${categoryName}"]`);
    if (parentWithCategory) {
      return true;
    }
    
    // فحص URL المنتج
    if (
      product.url.includes(`/category/${categoryId}`) || 
      product.url.includes(`?category=${categoryId}`) || 
      product.url.includes(`category_id=${categoryId}`) ||
      product.url.includes(`/categories/${categoryId}`)
    ) {
      return true;
    }
    
    // فحص اسم المنتج مع اسم الفئة
    const productNameLower = product.name.toLowerCase();
    if (productNameLower.includes(categoryName)) {
      return true;
    }
    
    // فحص كلمات الفئة في اسم المنتج
    const categoryWords = categoryName.split(/\s+/).filter(word => word.length > 2);
    let matchCount = 0;
    
    categoryWords.forEach(word => {
      if (productNameLower.includes(word)) {
        matchCount++;
      }
    });
    
    if (matchCount > 0 && categoryWords.length > 0) {
      const matchRatio = matchCount / categoryWords.length;
      if (matchRatio >= 0.5) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * معالجة حدث النقر على الفئة
   * @param {Object} category - الفئة المنقور عليها
   */
  handleCategoryClick(category) {
    console.log('تم النقر على الفئة:', category.name);
    
    // تحديث الفئة النشطة
    const isActive = this.activeCategory === category.id;
    
    // إزالة الحالة النشطة من جميع الأيقونات
    const allIcons = this.querySelectorAll('.category-icon');
    allIcons.forEach(icon => icon.classList.remove('active'));
    
    // إزالة جميع المنتجات النشطة
    const allProducts = this.querySelectorAll('.category-products');
    allProducts.forEach(products => {
      products.classList.remove('active');
      // إزالة العنصر بعد انتهاء التأثير
      setTimeout(() => products.remove(), 300);
    });
    
    // إذا تم النقر على نفس الفئة مرة أخرى، نخرج
    if (isActive) {
      this.activeCategory = null;
      return;
    }
    
    // تحديث الفئة النشطة
    this.activeCategory = category.id;
    
    // تحديث الأيقونة النشطة
    const activeIcon = this.querySelector(`.category-icon[data-category-id="${category.id}"]`);
    if (activeIcon) {
      activeIcon.classList.add('active');
    }
    
    // إنشاء حاوية المنتجات
    const productsDiv = document.createElement('div');
    productsDiv.className = 'category-products';
    productsDiv.dataset.categoryId = category.id;
    this.productsContainer.appendChild(productsDiv);
    
    // إظهار رسالة التحميل
    this.showLoading(productsDiv);
    
    // تأخير قصير ثم عرض المنتجات
    setTimeout(() => {
      productsDiv.classList.add('active');
    }, 10);
    
    // التحقق مما إذا كانت لدينا منتجات مخزنة لهذه الفئة
    if (this.products[category.id]) {
      this.renderProducts(category, this.products[category.id]);
    } else {
      // جلب منتجات الفئة
      this.loadCategoryProducts(category);
    }
  }

  /**
   * استخراج المنتجات من الصفحة الرئيسية للفئة
   * @param {Object} category - الفئة المستهدفة
   * @param {Element} productsDiv - حاوية المنتجات
   */
  extractProductsFromHomepage(category, productsDiv) {
    console.log('محاولة استخراج المنتجات من الصفحة الرئيسية للفئة:', category.name);
    
    // منتجات افتراضية للعرض عندما لا يتم العثور على منتجات حقيقية
    const dummyProducts = [
      {
        name: `بطاقة جوجل بلاي ${category.name}`,
        price: "100 ر.س",
        image: "https://cdn.salla.sa/dummy/vG2YBKzDNBggg46tQO0D6H1QhO8FyGXtMJyFCosC.png",
        url: "#",
        isGeneric: true
      },
      {
        name: `بطاقة ايتونز ${category.name}`,
        price: "150 ر.س",
        image: "https://cdn.salla.sa/dummy/goFsQl9CYL4pJKAoJCQg5YAQZ7oX2QPSHLhHIIwJ.png",
        url: "#",
        isGeneric: true
      },
      {
        name: `بطاقة ستور ${category.name}`,
        price: "200 ر.س",
        image: "https://cdn.salla.sa/dummy/tXoHFJsROvMJlQptQAqm3Z5nH3nKjmTulpZGaRfK.png",
        url: "#",
        isGeneric: true
      },
      {
        name: `بطاقة بلايستيشن ${category.name}`,
        price: "250 ر.س",
        image: "https://cdn.salla.sa/dummy/8FsKUxJVsLRdURdMBIEUNbfR2PjeWbkQnRcmxJYr.png",
        url: "#",
        isGeneric: true
      }
    ];
    
    // البحث عن المنتجات في الصفحة
    const allProductElements = document.querySelectorAll('.product-card, .product, [data-product-id], .s-product-card, .s-grid-item, .swiper-slide, .card, .item, [id*="product"], [class*="product"]');
    
    const realProducts = [];
    
    // محاولة استخراج المنتجات الفعلية
    this._searchInAvailableProducts(allProductElements, category, realProducts);
    this.extractProductsFromSections(category, realProducts);
    
    // استخدام المنتجات الحقيقية إذا وجدت، وإلا استخدام المنتجات الافتراضية
    if (realProducts.length > 0) {
      const uniqueProducts = this.removeDuplicateProducts(realProducts);
      console.log(`تم العثور على ${uniqueProducts.length} منتج فعلي للفئة "${category.name}"`);
      this.products[category.id] = uniqueProducts;
      this.renderProducts(category, uniqueProducts);
    } else {
      console.log(`استخدام المنتجات الافتراضية للفئة "${category.name}"`);
      const productsWithMessage = [{
        isMessage: true,
        name: `منتجات ${category.name}`,
        message: 'منتجات عرض توضيحي'
      }, ...dummyProducts];
      
      this.products[category.id] = productsWithMessage;
      this.renderProducts(category, productsWithMessage);
    }
  }

  /**
   * استخراج المنتجات من الأقسام التي تحتوي على اسم الفئة
   * @param {Object} category - الفئة المستهدفة
   * @param {Array} results - مصفوفة النتائج
   * @private
   */
  extractProductsFromSections(category, results) {
    // البحث عن الأقسام التي قد تحتوي على اسم الفئة
    const allSections = document.querySelectorAll('section, .section, .block, .row, .products-section, .products-block, .container, .content');
    const categoryNameLower = category.name.toLowerCase();
    const processedUrls = new Set(results.map(r => r.url));
    
    allSections.forEach(section => {
      try {
        // البحث عن عنوان القسم
        const sectionTitles = section.querySelectorAll('h1, h2, h3, h4, .title, .heading, .section-title');
        let matchingSection = false;
        
        sectionTitles.forEach(title => {
          const titleText = title.textContent.toLowerCase();
          if (titleText.includes(categoryNameLower)) {
            matchingSection = true;
          }
        });
        
        if (matchingSection) {
          // وجدنا قسمًا يحتوي على اسم الفئة، استخراج المنتجات منه
          const sectionProducts = section.querySelectorAll('.product-card, .product, [data-product-id], .s-product-card, .card, .item');
          console.log(`وجدنا قسمًا يحتوي على اسم الفئة "${category.name}" مع ${sectionProducts.length} منتج`);
          
          sectionProducts.forEach(productElement => {
            const productData = this.extractProductData(productElement);
            if (productData && !processedUrls.has(productData.url)) {
              processedUrls.add(productData.url);
              productData.confidence = 90; // ثقة عالية
              results.push(productData);
            }
          });
        }
      } catch (error) {
        console.error('خطأ في تحليل القسم:', error);
      }
    });
  }

  /**
   * يبحث عن منتجات البديلة من الروابط في الصفحة
   * @param {Object} category - الفئة المستهدفة
   * @param {Array} results - مصفوفة النتائج
   * @private
   */
  _searchInAllLinks(category, results) {
    // البحث في جميع الروابط التي قد تكون منتجات
    const allLinks = document.querySelectorAll('a[href]');
    const categoryNameLower = category.name.toLowerCase();
    const processedUrls = new Set(results.map(r => r.url));
    
    allLinks.forEach(link => {
      try {
        // تخطي الروابط المعالجة بالفعل
        if (processedUrls.has(link.href)) return;
        
        const linkUrl = link.href.toLowerCase();
        const linkText = link.textContent.trim();
        
        // التحقق مما إذا كان الرابط يشير إلى منتج
        const isProductLink = linkUrl.includes('/product/') || 
                              linkUrl.includes('/products/') || 
                              linkUrl.includes('product_id=');
        
        // التحقق من مطابقة كلمات الفئة
        let isRelatedToCategory = false;
        
        if (linkText.toLowerCase().includes(categoryNameLower)) {
          isRelatedToCategory = true;
        } else {
          // فحص كلمات الفئة في النص
          const categoryWords = categoryNameLower.split(/\s+/).filter(word => word.length > 2);
          let matchCount = 0;
          
          categoryWords.forEach(word => {
            if (linkText.toLowerCase().includes(word)) {
              matchCount++;
            }
          });
          
          if (matchCount > 0 && categoryWords.length > 0) {
            const matchRatio = matchCount / categoryWords.length;
            if (matchRatio >= 0.5) {
              isRelatedToCategory = true;
            }
          }
        }
        
        // إذا كان الرابط منتجًا ومرتبطًا بالفئة
        if (isProductLink && isRelatedToCategory) {
          // الحصول على الصورة من النص أو الصورة داخل الرابط
          let productImage = '';
          const imgElement = link.querySelector('img');
          if (imgElement) {
            productImage = imgElement.src;
          }
          
          // إضافة المنتج إلى النتائج
          processedUrls.add(link.href);
          results.push({
            name: linkText,
            url: link.href,
            image: productImage,
            thumbnail: productImage,
            confidence: 60,
            related: true
          });
        }
      } catch (error) {
        console.error('خطأ في معالجة الرابط:', error);
      }
    });
  }

  /**
   * استخدام أحدث المنتجات كبديل
   * طريقة محسنة
   * @param {Object} category - الفئة المستهدفة
   * @param {Element} productsDiv - حاوية المنتجات
   * @private
   */
  useLatestProductsAsFallback(category, productsDiv) {
    console.log('استخدام أحدث المنتجات كبديل لفئة:', category.name);
    
    // قائمة البحث المحسنة
    const selectors = [
      '.latest-products', 
      '.newest-products', 
      '.featured-products',
      '[data-section="latest-products"]',
      '.products-slider',
      '.product-slider',
      '.swiper-container',
      '.products-grid',
      '.products-container',
      '[class*="products"]',
    ];
    
    // محاولة أخيرة: البحث في روابط الصفحة
    const results = [];
    this._searchInAllLinks(category, results);
    
    if (results.length > 0) {
      console.log(`تم العثور على ${results.length} منتج عن طريق البحث في الروابط`);
      const productsWithMessage = [{
        isMessage: true,
        name: `منتجات متعلقة بـ ${category.name}`,
        message: 'منتجات متعلقة بالفئة'
      }, ...results.slice(0, 8)];
      
      this.products[category.id] = productsWithMessage;
      this.renderProducts(category, productsWithMessage);
      return;
    }
    
    let latestProducts = [];
    
    // جرب كل محدد
    for (const selector of selectors) {
      const section = document.querySelector(selector);
      if (section) {
        const products = section.querySelectorAll('.product-card, .product, [data-product-id], .card, .item');
        if (products.length > 0) {
          console.log(`وجدنا ${products.length} منتج في قسم "${selector}"`);
          products.forEach(element => {
            const productData = this.extractProductData(element);
            if (productData) {
              productData.isGeneric = true;
              latestProducts.push(productData);
            }
          });
          
          if (latestProducts.length > 0) {
            break;
          }
        }
      }
    }
    
    // إذا وجدنا منتجات
    if (latestProducts.length > 0) {
      const productsWithMessage = [{
        isMessage: true,
        name: 'منتجات متنوعة',
        message: 'لم يتم العثور على منتجات مخصصة لهذه الفئة، نعرض منتجات متنوعة بدلاً من ذلك'
      }, ...latestProducts.slice(0, 8)];
      
      this.products[category.id] = productsWithMessage;
      this.renderProducts(category, productsWithMessage);
    } else {
      // إذا لم نجد أي منتجات
      productsDiv.innerHTML = '<div class="p-4 text-center text-gray-500">لا توجد منتجات في هذه الفئة</div>';
    }
  }

  /**
   * عرض منتجات الفئة بشكل محسن
   * @param {Object} category - الفئة المستهدفة
   * @param {Array} products - منتجات الفئة
   */
  renderProducts(category, products) {
    console.log('عرض منتجات الفئة:', category.name, products.length);
    
    // العثور على حاوية المنتجات
    const productsDiv = this.querySelector(`.category-products[data-category-id="${category.id}"]`);
    if (!productsDiv) return;
    
    // مسح محتوى حاوية المنتجات
    productsDiv.innerHTML = '';
    
    if (!products || products.length === 0) {
      productsDiv.innerHTML = '<div class="p-4 text-center text-gray-500">لا توجد منتجات في هذه الفئة</div>';
      return;
    }
    
    // تحديد عدد المنتجات التي سيتم عرضها (بحد أقصى 8 منتجات)
    const maxDisplayProducts = 8;
    const displayProducts = products.slice(0, maxDisplayProducts);
    const hasMoreProducts = products.length > maxDisplayProducts;

    // إضافة عنوان الفئة
    const categoryHeader = document.createElement('div');
    categoryHeader.className = 'category-products-header';
    categoryHeader.innerHTML = `
      <h3 class="category-products-title">${category.name}</h3>
      <div class="category-products-subtitle">منتجات ${category.name}</div>
    `;
    productsDiv.appendChild(categoryHeader);

    // إنشاء شبكة المنتجات
    const productsGrid = document.createElement('div');
    productsGrid.className = 'products-grid';
    productsDiv.appendChild(productsGrid);

    // إنشاء بطاقات المنتجات
    displayProducts.forEach(product => {
      // التحقق إذا كان هذا رسالة خاصة
      if (product.isMessage) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'col-span-full p-2 mb-4 bg-gradient-to-r from-primary-50 to-primary-100 rounded-md text-center text-sm text-gray-700';
        messageDiv.innerHTML = `<strong>${product.name}:</strong> ${product.message}`;
        productsGrid.appendChild(messageDiv);
        return;
      }
      
      const productCard = document.createElement('a');
      productCard.href = product.url || '#';
      productCard.className = 'product-card hover:shadow-lg transition-all duration-300';
      productCard.setAttribute('data-product-id', product.id || '');
      
      // إضافة فئة إضافية للمنتجات غير المرتبطة
      if (product.isGeneric) {
        productCard.classList.add('generic-product');
      }
      
      // تحضير الصورة
      let imageHtml = '';
      const imageSrc = product.thumbnail || product.image || '';
      if (imageSrc) {
        imageHtml = `<img src="${imageSrc}" alt="${product.name}" loading="lazy" class="transition-transform duration-500" />`;
      } else {
        imageHtml = `<i class="sicon-box text-gray-400 text-2xl"></i>`;
      }
      
      // تحضير السعر
      let priceHtml = '';
      if (product.sale_price && product.regular_price) {
        // سعر مخفض
        priceHtml = `
          <div class="product-price">
            <span class="text-primary-500 font-bold">${product.sale_price}</span>
            <span class="line-through text-gray-400 mr-1 rtl:mr-0 rtl:ml-1 text-sm">${product.regular_price}</span>
          </div>
        `;
      } else if (product.price) {
        // سعر عادي
        priceHtml = `<div class="product-price">${product.price}</div>`;
      }
      
      // إضافة علامة العرض الترويجي إذا كان متاحًا
      let promoHtml = '';
      if (product.promotion) {
        promoHtml = `<div class="absolute top-2 right-2 rtl:right-auto rtl:left-2 bg-red-500 text-white text-xs py-1 px-2 rounded-full">عرض</div>`;
      }
      
      // إضافة مؤشر الكمية المتوفرة
      let stockHtml = '';
      if (product.quantity !== undefined) {
        if (product.quantity <= 0) {
          stockHtml = `<div class="absolute bottom-2 right-2 rtl:right-auto rtl:left-2 bg-gray-500 text-white text-xs py-1 px-2 rounded-full">نفذت الكمية</div>`;
        } else if (product.quantity < 5) {
          stockHtml = `<div class="absolute bottom-2 right-2 rtl:right-auto rtl:left-2 bg-yellow-500 text-white text-xs py-1 px-2 rounded-full">كمية محدودة</div>`;
        }
      }
      
      // زر إضافة للسلة (للتفاعل)
      const addToCartButton = `
        <button class="add-to-cart-btn" title="أضف إلى السلة">
          <i class="sicon-cart-add"></i>
        </button>
      `;
      
      // إنشاء قالب بطاقة المنتج
      productCard.innerHTML = `
        <div class="product-image relative">
          ${imageHtml}
          ${promoHtml}
          ${stockHtml}
          <div class="product-actions">
            ${addToCartButton}
          </div>
        </div>
        <div class="product-details">
          <div class="product-name">${product.name}</div>
          ${priceHtml}
        </div>
      `;
      
      // إضافة مستمع حدث النقر إذا كان المنتج بلا رابط
      if (product.url === '#' || !product.url) {
        productCard.addEventListener('click', (e) => {
          e.preventDefault();
          this.showProductDetails(product);
        });
      }
      
      // إضافة مستمع أحداث لزر إضافة للسلة
      productCard.querySelector('.add-to-cart-btn')?.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.handleAddToCart(product);
      });
      
      productsGrid.appendChild(productCard);
    });

    // إضافة زر "عرض المزيد" في حالة وجود المزيد من المنتجات
    if (hasMoreProducts || products.length > 0) {
      const viewMoreContainer = document.createElement('div');
      viewMoreContainer.className = 'col-span-full mt-4 mb-2 flex justify-center';
      
      const viewMoreBtn = document.createElement('a');
      viewMoreBtn.href = category.url || `${window.location.origin}/products?category=${category.id}`;
      viewMoreBtn.className = 'view-more-btn';
      viewMoreBtn.textContent = 'عرض كل منتجات الفئة';
      viewMoreBtn.style.backgroundColor = 'rgb(var(--primary-color))';

      viewMoreContainer.appendChild(viewMoreBtn);
      productsDiv.appendChild(viewMoreContainer);
    }
  }

  /**
   * معالجة إضافة منتج للسلة
   * @param {Object} product - المنتج المراد إضافته للسلة
   */
  handleAddToCart(product) {
    console.log('إضافة المنتج للسلة:', product.name);
    
    // عرض رسالة تأكيد إضافة المنتج
    const notification = document.createElement('div');
    notification.className = 'cart-notification';
    notification.innerHTML = `
      <div class="cart-notification-content">
        <i class="sicon-check-circle cart-notification-icon"></i>
        <div class="cart-notification-text">
          <p>تمت إضافة المنتج للسلة بنجاح</p>
          <strong>${product.name}</strong>
        </div>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // عرض الإشعار لفترة ثم إزالته
    setTimeout(() => {
      notification.classList.add('show');
      
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 2000);
    }, 10);
    
    // إذا كان SDK سلة متاح، محاولة إضافة المنتج للسلة
    try {
      if (window.salla && window.salla.cart) {
        // الحصول على معرف المنتج من الرابط إذا كان ممكنًا
        let productId = product.id;
        if (!productId && product.url) {
          // محاولة استخراج المعرف من الرابط
          const match = product.url.match(/\/products\/(\d+)/);
          if (match && match[1]) {
            productId = match[1];
          }
        }
        
        if (productId) {
          window.salla.cart.addItem(productId, 1)
            .then(() => console.log('تمت إضافة المنتج للسلة'))
            .catch(error => console.error('خطأ في إضافة المنتج للسلة:', error));
        }
      }
    } catch (error) {
      console.error('خطأ في محاولة إضافة المنتج للسلة:', error);
    }
  }
}

// إضافة طرق الصنف باستخدام createClass helper
createClass(CategoryFilter, [
  { key: "loadCategories", value: function loadCategories() {
    console.log('جاري تحميل الفئات');
    
    // إظهار رسالة التحميل
    this.showLoading(this.filterContainer);
    
    try {
      // محاولة جلب الفئات باستخدام Twilight SDK
      if (window.salla && window.salla.config && window.salla.config.get) {
        const url = window.salla.url.get('categories');
        console.log('الحصول على الفئات من:', url);
        
        fetch(url)
          .then(response => {
            if (!response.ok) {
              throw new Error('فشل في الحصول على الفئات');
            }
            return response.json();
          })
          .then(result => {
            console.log('نتائج الفئات:', result);
            
            if (result && result.data && Array.isArray(result.data)) {
              this.categories = result.data.filter(cat => cat.status === 'active');
              this.renderCategories();
            } else {
              // إذا لم نتمكن من جلب الفئات، نحاول البحث محليًا
              this.findLocalCategories();
            }
          })
          .catch(error => {
            console.error('خطأ في الحصول على الفئات:', error);
            // في حالة الخطأ، نحاول البحث محليًا
            this.findLocalCategories();
          });
      } else if (window.twilight && window.twilight.category) {
        // الطريقة البديلة باستخدام واجهة تويلايت للفئات
        window.twilight.category.categories()
          .then(result => {
            console.log('نتائج الفئات (twilight):', result);
            
            if (result && result.data) {
              this.categories = result.data.filter(cat => cat.status === 'active');
              this.renderCategories();
            } else {
              this.findLocalCategories();
            }
          })
          .catch(error => {
            console.error('خطأ في جلب الفئات (twilight):', error);
            this.findLocalCategories();
          });
      } else {
        // إذا لم نتمكن من استخدام Twilight، نحاول البحث محليًا
        this.findLocalCategories();
      }
    } catch (error) {
      console.error('خطأ في جلب الفئات:', error);
      this.findLocalCategories();
    }
  }},
  { key: "findLocalCategories", value: function findLocalCategories() {
    console.log('البحث عن الفئات في الصفحة');
    
    // البحث عن الفئات في صفحة الموقع
    const categoryElements = document.querySelectorAll('.category-entry, .category-card, [data-category-id], .nav-link, .categories-menu a, .main-menu a');
    const categories = [];
    
    categoryElements.forEach(element => {
      const categoryName = element.textContent.trim();
      const categoryLink = element.href || '#';
      const categoryImage = element.querySelector('img')?.src || '';
      const categoryId = element.dataset.categoryId || categories.length + 1;
      
      // تجنب تكرار الفئات
      if (!categories.some(cat => cat.name === categoryName) && categoryName.length > 0) {
        categories.push({
          id: categoryId,
          name: categoryName,
          url: categoryLink,
          image: categoryImage
        });
      }
    });
    
    console.log('تم العثور على الفئات المحلية:', categories);
    this.categories = categories;
    this.renderCategories();
  }},
  { key: "renderCategories", value: function renderCategories() {
    // إزالة رسالة التحميل
    this.filterContainer.innerHTML = '';
    
    if (this.categories.length === 0) {
      this.filterContainer.innerHTML = '<div class="text-gray-500">لا توجد فئات متاحة</div>';
      return;
    }
    
    console.log('عرض الفئات:', this.categories.length);
    
    // إنشاء أيقونات الفئات
    this.categories.forEach(category => {
      const categoryIcon = document.createElement('div');
      categoryIcon.className = 'category-icon';
      categoryIcon.dataset.categoryId = category.id;
      
      let imageHtml = '';
      if (category.image) {
        imageHtml = `<img src="${category.image}" alt="${category.name}" />`;
      } else {
        imageHtml = `<i class="sicon-folder text-gray-400 text-xl"></i>`;
      }
      
      categoryIcon.innerHTML = `
        <div class="category-icon-image">
          ${imageHtml}
        </div>
        <div class="category-icon-name">${category.name}</div>
      `;
      
      // إضافة مستمع حدث النقر
      categoryIcon.addEventListener('click', () => this.handleCategoryClick(category));
      
      this.filterContainer.appendChild(categoryIcon);
    });
  }},
  { key: "handleCategoryClick", value: function handleCategoryClick(category) {
    console.log('تم النقر على الفئة:', category.name);
    
    // تحديث الفئة النشطة
    const isActive = this.activeCategory === category.id;
    
    // إزالة الحالة النشطة من جميع الأيقونات
    const allIcons = this.querySelectorAll('.category-icon');
    allIcons.forEach(icon => icon.classList.remove('active'));
    
    // إزالة جميع المنتجات النشطة
    const allProducts = this.querySelectorAll('.category-products');
    allProducts.forEach(products => {
      products.classList.remove('active');
      // إزالة العنصر بعد انتهاء التأثير
      setTimeout(() => products.remove(), 300);
    });
    
    // إذا تم النقر على نفس الفئة مرة أخرى، نخرج
    if (isActive) {
      this.activeCategory = null;
      return;
    }
    
    // تحديث الفئة النشطة
    this.activeCategory = category.id;
    
    // تحديث الأيقونة النشطة
    const activeIcon = this.querySelector(`.category-icon[data-category-id="${category.id}"]`);
    if (activeIcon) {
      activeIcon.classList.add('active');
    }
    
    // إنشاء حاوية المنتجات
    const productsDiv = document.createElement('div');
    productsDiv.className = 'category-products';
    productsDiv.dataset.categoryId = category.id;
    this.productsContainer.appendChild(productsDiv);
    
    // إظهار رسالة التحميل
    this.showLoading(productsDiv);
    
    // تأخير قصير ثم عرض المنتجات
    setTimeout(() => {
      productsDiv.classList.add('active');
    }, 10);
    
    // التحقق مما إذا كانت لدينا منتجات مخزنة لهذه الفئة
    if (this.products[category.id]) {
      this.renderProducts(category, this.products[category.id]);
    } else {
      // جلب منتجات الفئة
      this.loadCategoryProducts(category);
    }
  }},
  { key: "loadCategoryProducts", value: function loadCategoryProducts(category) {
    console.log('جاري تحميل منتجات الفئة:', category.name, 'ID:', category.id);
    
    // إنشاء حاوية المنتجات
    const productsDiv = document.createElement('div');
    productsDiv.className = 'category-products';
    productsDiv.dataset.categoryId = category.id;
    this.productsContainer.appendChild(productsDiv);
    
    // إظهار رسالة التحميل
    this.showLoading(productsDiv);
    
    // تأخير قصير ثم عرض المنتجات
    setTimeout(() => {
      productsDiv.classList.add('active');
    }, 10);

    // حل جديد: محاولة استخراج المنتجات من الصفحة الرئيسية مباشرة
    this.extractProductsFromHomepage(category, productsDiv);
  }},
  
  { key: "extractProductData", value: function extractProductData(element) {
    const name = this.extractProductName(element);
    if (!name) return null;
    
    const url = this.extractProductLink(element);
    const image = this.extractProductImage(element);
    const price = this.extractProductPrice(element);
    
    return {
      name: name,
      url: url,
      image: image,
      thumbnail: image,
      price: price,
      related: true
    };
  }},
  
  { key: "extractProductsFromSections", value: function extractProductsFromSections(category, results) {
    // البحث عن الأقسام التي قد تحتوي على اسم الفئة
    const allSections = document.querySelectorAll('section, .section, .block, .row, .products-section, .products-block, .container, .content');
    const categoryNameLower = category.name.toLowerCase();
    const processedUrls = new Set(results.map(r => r.url));
    
    allSections.forEach(section => {
      try {
        // البحث عن عنوان القسم
        const sectionTitles = section.querySelectorAll('h1, h2, h3, h4, .title, .heading, .section-title');
        let matchingSection = false;
        
        sectionTitles.forEach(title => {
          const titleText = title.textContent.toLowerCase();
          if (titleText.includes(categoryNameLower)) {
            matchingSection = true;
          }
        });
        
        if (matchingSection) {
          // وجدنا قسمًا يحتوي على اسم الفئة، استخراج المنتجات منه
          const sectionProducts = section.querySelectorAll('.product-card, .product, [data-product-id], .s-product-card, .card, .item');
          console.log(`وجدنا قسمًا يحتوي على اسم الفئة "${category.name}" مع ${sectionProducts.length} منتج`);
          
          sectionProducts.forEach(productElement => {
            const productData = this.extractProductData(productElement);
            if (productData && !processedUrls.has(productData.url)) {
              processedUrls.add(productData.url);
              productData.confidence = 90; // ثقة عالية
              results.push(productData);
            }
          });
        }
      } catch (error) {
        console.error('خطأ في تحليل القسم:', error);
      }
    });
  }},
  
  { key: "isProductRelatedToCategory", value: function isProductRelatedToCategory(element, product, categoryId, categoryName) {
    // فحص السمات المباشرة
    if (
      element.dataset.categoryId === categoryId || 
      element.getAttribute('data-category') === categoryId ||
      element.dataset.category === categoryId
    ) {
      return true;
    }
    
    // فحص عناصر الأب
    const parentWithCategory = element.closest(`[data-category-id="${categoryId}"], [data-category="${categoryId}"], [data-category-slug="${categoryName}"]`);
    if (parentWithCategory) {
      return true;
    }
    
    // فحص URL المنتج
    if (
      product.url.includes(`/category/${categoryId}`) || 
      product.url.includes(`?category=${categoryId}`) || 
      product.url.includes(`category_id=${categoryId}`) ||
      product.url.includes(`/categories/${categoryId}`)
    ) {
      return true;
    }
    
    // فحص اسم المنتج مع اسم الفئة
    const productNameLower = product.name.toLowerCase();
    if (productNameLower.includes(categoryName)) {
      return true;
    }
    
    // فحص كلمات الفئة في اسم المنتج
    const categoryWords = categoryName.split(/\s+/).filter(word => word.length > 2);
    let matchCount = 0;
    
    categoryWords.forEach(word => {
      if (productNameLower.includes(word)) {
        matchCount++;
      }
    });
    
    if (matchCount > 0 && categoryWords.length > 0) {
      const matchRatio = matchCount / categoryWords.length;
      if (matchRatio >= 0.5) {
        return true;
      }
    }
    
    return false;
  }},
  
  { key: "useLatestProductsAsFallback", value: function useLatestProductsAsFallback(category, productsDiv) {
    console.log('استخدام أحدث المنتجات كبديل لفئة:', category.name);
    
    // قائمة البحث المحسنة
    const selectors = [
      '.latest-products', 
      '.newest-products', 
      '.featured-products',
      '[data-section="latest-products"]',
      '.products-slider',
      '.product-slider',
      '.swiper-container',
      '.products-grid',
      '.products-container',
      '[class*="products"]',
    ];
    
    // محاولة أخيرة: البحث في روابط الصفحة
    const results = [];
    this._searchInAllLinks(category, results);
    
    if (results.length > 0) {
      console.log(`تم العثور على ${results.length} منتج عن طريق البحث في الروابط`);
      const productsWithMessage = [{
        isMessage: true,
        name: `منتجات متعلقة بـ ${category.name}`,
        message: 'منتجات متعلقة بالفئة'
      }, ...results.slice(0, 8)];
      
      this.products[category.id] = productsWithMessage;
      this.renderProducts(category, productsWithMessage);
      return;
    }
    
    let latestProducts = [];
    
    // جرب كل محدد
    for (const selector of selectors) {
      const section = document.querySelector(selector);
      if (section) {
        const products = section.querySelectorAll('.product-card, .product, [data-product-id], .card, .item');
        if (products.length > 0) {
          console.log(`وجدنا ${products.length} منتج في قسم "${selector}"`);
          products.forEach(element => {
            const productData = this.extractProductData(element);
            if (productData) {
              productData.isGeneric = true;
              latestProducts.push(productData);
            }
          });
          
          if (latestProducts.length > 0) {
            break;
          }
        }
      }
    }
    
    // إذا وجدنا منتجات
    if (latestProducts.length > 0) {
      const productsWithMessage = [{
        isMessage: true,
        name: 'منتجات متنوعة',
        message: 'لم يتم العثور على منتجات مخصصة لهذه الفئة، نعرض منتجات متنوعة بدلاً من ذلك'
      }, ...latestProducts.slice(0, 8)];
      
      this.products[category.id] = productsWithMessage;
      this.renderProducts(category, productsWithMessage);
    } else {
      // إذا لم نجد أي منتجات
      productsDiv.innerHTML = '<div class="p-4 text-center text-gray-500">لا توجد منتجات في هذه الفئة</div>';
    }
  }},
  
  { key: "removeDuplicateProducts", value: function removeDuplicateProducts(products) {
    // إزالة المنتجات المكررة بناءً على URL أو الاسم
    const uniqueUrls = new Set();
    const uniqueNames = new Set();
    const uniqueProducts = [];
    
    products.forEach(product => {
      // استخدام مزيج من URL واسم المنتج للتحقق من التكرار
      const productUrl = product.url;
      const productName = product.name;
      
      if (!uniqueUrls.has(productUrl) && !uniqueNames.has(productName)) {
        uniqueUrls.add(productUrl);
        uniqueNames.add(productName);
        uniqueProducts.push(product);
      }
    });
    
    return uniqueProducts;
  }},
  
  { key: "extractProductName", value: function extractProductName(element) {
    // تحسين استخراج اسم المنتج
    const nameSelectors = [
      '.product-title', 
      '.product-name', 
      'h3', 
      'h2',
      '.title',
      '.name',
      'a[title]',
      '.s-product-card-title', 
      '.card-title',
      '.product-info h4',
      '.product-info .title'
    ];
    
    // محاولة كل انتقاء
    for (const selector of nameSelectors) {
      const nameEl = element.querySelector(selector);
      if (nameEl && nameEl.textContent.trim()) {
        return nameEl.textContent.trim();
      }
    }
    
    // محاولات إضافية
    if (element.dataset.productTitle) {
      return element.dataset.productTitle;
    }
    
    if (element.getAttribute('aria-label')) {
      return element.getAttribute('aria-label');
    }
    
    if (element.querySelector('img') && element.querySelector('img').alt) {
      return element.querySelector('img').alt;
    }
    
    // نحتفظ بهذا كملاذ أخير
    const text = element.textContent.trim();
    if (text && text.length < 100) { // تجنب النصوص الطويلة جدًا
      return text;
    }
    
    return '';
  }},
  
  { key: "extractProductLink", value: function extractProductLink(element) {
    // تحسين استخراج رابط المنتج
    
    // إذا كان العنصر نفسه رابطًا
    if (element.tagName === 'A' && element.href) {
      return element.href;
    }
    
    // البحث عن رابط داخل العنصر
    const linkEl = element.querySelector('a[href]');
    if (linkEl && linkEl.href) {
      return linkEl.href;
    }
    
    // البحث عن الرابط في السمات
    if (element.getAttribute('href')) {
      return element.getAttribute('href');
    }
    
    if (element.dataset.href) {
      return element.dataset.href;
    }
    
    if (element.dataset.link) {
      return element.dataset.link;
    }
    
    if (element.dataset.url) {
      return element.dataset.url;
    }
    
    // البحث عن عنصر يحتوي على سمة data-product-url
    const productUrlEl = element.querySelector('[data-product-url]');
    if (productUrlEl && productUrlEl.dataset.productUrl) {
      return productUrlEl.dataset.productUrl;
    }
    
    // إذا كان لدينا معرف للمنتج، يمكننا تخمين الرابط
    if (element.dataset.productId) {
      return `${window.location.origin}/products/${element.dataset.productId}`;
    }
    
    // إذا لم نجد رابطًا
    return '#';
  }},
  
  { key: "extractProductImage", value: function extractProductImage(element) {
    // تحسين استخراج صورة المنتج
    
    // البحث عن صورة
    const imgEl = element.querySelector('img');
    if (imgEl) {
      // تحقق من عدة سمات للصورة
      return imgEl.src || 
             imgEl.dataset.src || 
             imgEl.getAttribute('data-src') || 
             imgEl.getAttribute('data-lazy-src') || 
             '';
    }
    
    // البحث عن عنصر بخلفية CSS
    const elements = [element, ...Array.from(element.querySelectorAll('*'))];
    for (const el of elements) {
      const style = window.getComputedStyle(el);
      if (style.backgroundImage && style.backgroundImage !== 'none') {
        const match = style.backgroundImage.match(/url\(['"]?(.+?)['"]?\)/);
        if (match && match[1]) {
          return match[1];
        }
      }
    }
    
    // البحث عن سمات أخرى
    if (element.dataset.image) {
      return element.dataset.image;
    }
    
    const imageEl = element.querySelector('[data-image]');
    if (imageEl && imageEl.dataset.image) {
      return imageEl.dataset.image;
    }
    
    return '';
  }},
  
  { key: "extractProductPrice", value: function extractProductPrice(element) {
    // تحسين استخراج سعر المنتج
    const priceSelectors = [
      '.product-price', 
      '.price', 
      '.s-product-card-price', 
      '.card-price',
      '.product-info .price',
      '[data-price]',
      '.amount',
      '.product-amount'
    ];
    
    // محاولة كل انتقاء
    for (const selector of priceSelectors) {
      const priceEl = element.querySelector(selector);
      if (priceEl && priceEl.textContent.trim()) {
        return priceEl.textContent.trim();
      }
    }
    
    // محاولات إضافية
    if (element.dataset.price) {
      return element.dataset.price;
    }
    
    const priceDataEl = element.querySelector('[data-price]');
    if (priceDataEl && priceDataEl.dataset.price) {
      return priceDataEl.dataset.price;
    }
    
    return '';
  }},
  { key: "showLoading", value: function showLoading(container) {
    container.innerHTML = `
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    `;
  }},
  { key: "showProductDetails", value: function showProductDetails(product) {
    console.log('عرض تفاصيل المنتج:', product);
    
    // إنشاء طبقة مظلمة
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
    
    // إنشاء نافذة تفاصيل المنتج
    const modal = document.createElement('div');
    modal.className = 'bg-white rounded-lg p-4 max-w-md w-full max-h-90vh overflow-auto relative';
    
    // زر الإغلاق
    const closeButton = document.createElement('button');
    closeButton.className = 'absolute top-2 right-2 rtl:right-auto rtl:left-2 text-gray-500 hover:text-gray-800';
    closeButton.innerHTML = '<i class="sicon-cancel text-xl"></i>';
    closeButton.addEventListener('click', () => {
      document.body.removeChild(overlay);
    });
    
    // صورة المنتج
    let imageHtml = '';
    if (product.image) {
      imageHtml = `<img src="${product.image}" alt="${product.name}" class="w-full h-auto rounded-md max-h-60 object-contain mx-auto">`;
    } else {
      imageHtml = `<div class="w-full h-48 bg-gray-200 rounded-md flex items-center justify-center"><i class="sicon-box text-gray-400 text-4xl"></i></div>`;
    }
    
    // سعر المنتج
    let priceHtml = '';
    if (product.sale_price && product.regular_price) {
      priceHtml = `
        <div class="mt-4 flex items-center">
          <span class="text-primary-500 font-bold text-xl">${product.sale_price}</span>
          <span class="line-through text-gray-400 mr-2 rtl:mr-0 rtl:ml-2">${product.regular_price}</span>
        </div>
      `;
    } else if (product.price) {
      priceHtml = `<div class="mt-4 text-primary-500 font-bold text-xl">${product.price}</div>`;
    }
    
    // حالة المخزون
    let stockHtml = '';
    if (product.quantity !== undefined) {
      if (product.quantity <= 0) {
        stockHtml = `<div class="mt-2 text-red-500">غير متوفر في المخزون</div>`;
      } else if (product.quantity < 5) {
        stockHtml = `<div class="mt-2 text-yellow-500">متبقي ${product.quantity} قطع فقط</div>`;
      } else {
        stockHtml = `<div class="mt-2 text-green-500">متوفر في المخزون</div>`;
      }
    }
    
    // زر الانتقال للمنتج
    let buttonHtml = '';
    if (product.url && product.url !== '#') {
      buttonHtml = `
        <a href="${product.url}" class="block w-full mt-4 py-2 px-4 bg-primary-500 hover:bg-primary-600 text-white text-center rounded-md transition-colors duration-300">
          عرض المنتج في المتجر
        </a>
      `;
    }
    
    // HTML النافذة المنبثقة
    modal.innerHTML = `
      <div class="product-modal-content">
        ${imageHtml}
        <h3 class="text-xl font-bold mt-4">${product.name}</h3>
        ${priceHtml}
        ${stockHtml}
        ${buttonHtml}
      </div>
    `;
    
    modal.appendChild(closeButton);
    overlay.appendChild(modal);
    document.body.appendChild(overlay);
    
    // إضافة مستمع حدث النقر على الطبقة المظلمة لإغلاق النافذة
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay);
      }
    });
    
    // إضافة مستمع حدث ضغط الزر Escape لإغلاق النافذة
    const escHandler = (e) => {
      if (e.key === 'Escape') {
        document.body.removeChild(overlay);
        document.removeEventListener('keydown', escHandler);
      }
    };
    document.addEventListener('keydown', escHandler);
  }}
]);

// تسجيل المكون المخصص
customElements.define('category-filter', CategoryFilter);

export default CategoryFilter; 