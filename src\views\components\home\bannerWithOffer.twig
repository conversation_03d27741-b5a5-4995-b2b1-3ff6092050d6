{# Debug information - سيتم إخفاء هذا القسم ولكنه يساعد في معرفة المتغيرات المتاحة #}
{# <div style="display: none;">
    Component: {{ component|json_encode }}
    Offer: {{ offer|json_encode }}
    Timer: {{ timer|json_encode }}
</div> #}

{# القيم الافتراضية للون الخلفية وعدد أيام العرض #}
{% set bg_color = '#1DE9B6' %}
{% set days = 5 %}
{% set overlay_opacity = '0.7' %}

{# الحصول على لون الخلفية من component.timer.bg إذا كان موجوداً #}
{% if component.timer is defined and component.timer.bg is defined %}
    {% set bg_color = component.timer.bg %}
{% endif %}

{# الحصول على عدد الأيام من component.timer.days إذا كان موجوداً #}
{% if component.timer is defined and component.timer.days is defined %}
    {% set days = component.timer.days %}
{% endif %}

{# محاولة الحصول على بيانات المؤقت من مصادر أخرى إذا لم تكن موجودة في component.timer #}
{% set timer_data = null %}

{% if component.timer is defined and component.timer|length > 0 and component.timer[0] is defined and component.timer[0] is iterable %}
    {% set timer_data = component.timer[0] %}
{% elseif timer is defined %}
    {% set timer_data = timer %}
{% endif %}

{# تعيين القيم من البيانات المسترجعة إذا كانت متوفرة ولم يتم تعيينها من قبل #}
{% if timer_data is not null %}
    {% if timer_data.bg is defined and bg_color == '#1DE9B6' %}
        {% set bg_color = timer_data.bg %}
    {% endif %}
    {% if timer_data.days is defined and days == 5 %}
        {% set days = timer_data.days %}
    {% endif %}
{% endif %}

{% set unique_id = component.id|default('offer-' ~ random()) %}

{# تجميع جميع الأنماط في بداية المكون #}
{# تعريف الألوان مع الشفافية بطريقة أبسط وأكثر أماناً #}
{% set bg_color_light = bg_color ~ '80' %}
{% set bg_color_dark = bg_color ~ '90' %}

<style>
    /* أنماط المكون الرئيسية */
    #offer-component-{{ unique_id|escape('html_attr') }} .offer-container {
        position: relative;
        border-radius: 8px;
        margin: 20px 0;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 20px {{ bg_color }}40;
        overflow: hidden;
        background-color: #121212;
        border: 1px solid {{ bg_color }};
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .banner-image {
        width: 100%;
        height: 400px;
        object-fit: cover;
        display: block;
        filter: brightness(0.7) contrast(1.1);
        transition: all 0.3s ease;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #121212;
        opacity: {{ overlay_opacity }};
        z-index: 1;
        background-image: 
            linear-gradient(rgba(18, 18, 18, 0.7), rgba(18, 18, 18, 0.9)),
            url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='{{ bg_color }}10' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E"),
            linear-gradient(45deg, {{ bg_color }}05 25%, transparent 25%, transparent 75%, {{ bg_color }}05 75%, {{ bg_color }}05),
            linear-gradient(45deg, {{ bg_color }}05 25%, transparent 25%, transparent 75%, {{ bg_color }}05 75%, {{ bg_color }}05);
        background-size: auto, auto, 60px 60px, 60px 60px;
        background-position: center, center, 0 0, 30px 30px;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .offer-content {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        color: white;
        text-align: center;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .offer-title {
        font-size: 28px;
        margin-bottom: 20px;
        text-shadow: 0 0 10px {{ bg_color }}, 0 0 20px {{ bg_color }}, 0 0 30px {{ bg_color }};
        font-weight: 700;
        letter-spacing: 1px;
        color: white;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .countdown-timer {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    /* أنماط العد التنازلي */
    #offer-component-{{ unique_id|escape('html_attr') }} .countdown-box {
        text-align: center;
        margin: 0 10px 10px;
        background: rgba(18, 18, 18, 0.7);
        color: white;
        border-radius: 12px;
        padding: 15px;
        min-width: 70px;
        box-shadow: 0 0 10px {{ bg_color }}60, 0 0 20px {{ bg_color }}20, inset 0 0 15px rgba(0, 0, 0, 0.5);
        position: relative;
        overflow: hidden;
        transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1),
                  box-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1);
        will-change: transform, box-shadow;
        transform: translateZ(0); /* Hardware acceleration */
        border: 1px solid {{ bg_color }};
    }
    
    #offer-component-{{ unique_id|escape('html_attr') }} .countdown-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 0 15px {{ bg_color }}90, 0 0 30px {{ bg_color }}30;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .countdown-value {
        font-size: 28px;
        font-weight: bold;
        text-shadow: 0 0 10px {{ bg_color }}, 0 0 5px {{ bg_color }};
        color: {{ bg_color }};
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .countdown-label {
        display: block;
        font-size: 14px;
        margin-top: 5px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* أنماط الزخارف */
    #offer-component-{{ unique_id|escape('html_attr') }} .bubble-decoration {
        position: absolute;
        background-color: {{ bg_color }}20;
        border-radius: 50%;
        border: 1px solid {{ bg_color }}40;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .bubble-top-right {
        top: -20px;
        right: -20px;
        width: 40px;
        height: 40px;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .bubble-top-right-medium {
        top: -15px;
        right: -15px;
        width: 30px;
        height: 30px;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .bubble-top-right-small {
        top: -10px;
        right: -10px;
        width: 25px;
        height: 25px;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .bubble-bottom-left {
        bottom: -10px;
        left: -10px;
        width: 25px;
        height: 25px;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .bubble-bottom-left-large {
        bottom: -20px;
        left: -20px;
        width: 40px;
        height: 40px;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .bubble-bottom-left-medium {
        bottom: -15px;
        left: -15px;
        width: 30px;
        height: 30px;
    }

    /* أنماط شارة نهاية العرض */
    #offer-component-{{ unique_id|escape('html_attr') }} .offer-ended-badge {
        position: absolute;
        top: -25px;
        right: -25px;
        background: linear-gradient(135deg, #121212, #333333);
        color: {{ bg_color }};
        border-radius: 50%;
        width: 120px;
        height: 120px;
        display: none;
        justify-content: center;
        align-items: center;
        transform: rotate(15deg);
        box-shadow: 0 0 15px {{ bg_color }}60, 0 0 30px {{ bg_color }}30;
        border: 2px dashed {{ bg_color }};
        overflow: hidden;
        z-index: 3;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .badge-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(45deg, {{ bg_color }}10, {{ bg_color }}10 10px, transparent 10px, transparent 20px);
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .badge-text {
        font-weight: bold;
        text-align: center;
        font-size: 18px;
        position: relative;
        text-shadow: 0 0 10px {{ bg_color }}, 0 0 20px {{ bg_color }};
    }

    /* أنماط الزر */
    #offer-component-{{ unique_id|escape('html_attr') }} .offer-cta-button {
        display: inline-block;
        background: rgba(18, 18, 18, 0.7);
        color: {{ bg_color }};
        padding: 12px 30px;
        border-radius: 50px;
        margin-top: 20px;
        text-decoration: none;
        font-weight: bold;
        position: relative;
        overflow: hidden;
        box-shadow: 0 0 10px {{ bg_color }}60, 0 0 20px {{ bg_color }}20;
        transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1),
                  box-shadow 0.3s cubic-bezier(0.2, 0, 0.2, 1),
                  background 0.3s ease;
        will-change: transform, box-shadow;
        transform: translateY(0);
        border: 1px solid {{ bg_color }};
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .offer-cta-button:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 0 15px {{ bg_color }}90, 0 0 30px {{ bg_color }}40;
        background: rgba(29, 233, 182, 0.2);
        color: white;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .offer-cta-button:active {
        transform: translateY(1px);
        box-shadow: 0 0 5px {{ bg_color }}60;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .button-text {
        position: relative;
        z-index: 2;
    }

    /* أنماط تفضيلات تقليل الحركة */
    html.reduced-motion #offer-component-{{ unique_id|escape('html_attr') }} .countdown-box,
    html.reduced-motion #offer-component-{{ unique_id|escape('html_attr') }} .offer-cta-button {
        transition: none !important;
    }

    /* إضافة تأثير الجزيئات */
    #offer-component-{{ unique_id|escape('html_attr') }} .particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
    }

    #offer-component-{{ unique_id|escape('html_attr') }} .particle {
        position: absolute;
        width: 3px;
        height: 3px;
        background-color: {{ bg_color }};
        border-radius: 50%;
        box-shadow: 0 0 10px {{ bg_color }}, 0 0 20px {{ bg_color }};
        opacity: 0;
        animation: float 15s infinite ease-in-out;
    }

    @keyframes float {
        0% {
            opacity: 0;
            transform: translateY(0) translateX(0);
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            opacity: 0;
            transform: translateY(-100px) translateX(20px);
        }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        #offer-component-{{ unique_id|escape('html_attr') }} .banner-image {
            height: 300px;
        }
        
        #offer-component-{{ unique_id|escape('html_attr') }} .offer-title {
            font-size: 22px;
        }
        
        #offer-component-{{ unique_id|escape('html_attr') }} .countdown-box {
            min-width: 60px;
            padding: 10px;
        }
        
        #offer-component-{{ unique_id|escape('html_attr') }} .countdown-value {
            font-size: 22px;
        }
    }
</style>

<script>
    // تعريف المتغيرات التي سيتم استخدامها لاحقاً
    window.offerBannerConfig = window.offerBannerConfig || {};
    window.offerBannerConfig['{{ unique_id }}'] = {
        bgColor: '{{ bg_color }}',
        days: parseInt('{{ days }}')
    };

    // التحقق من تفضيلات تقليل الحركة وإضافة الفئة المناسبة
    if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.documentElement.classList.add('reduced-motion');
    }
</script>

<div class="container" id="offer-component-{{ unique_id }}">
    <div class="offer-container">
        {# تبسيط الوصول إلى متغيرات العرض #}
        {% set offer_img = null %}
        {% set offer_url = '#' %}
        {% set offer_data = null %}

        {# محاولة الحصول على بيانات العرض من مصادر مختلفة #}
        {% if component.offer is defined and component.offer|length > 0 and component.offer[0] is defined %}
            {% set offer_data = component.offer[0] %}
        {% elseif component.offer is defined %}
            {% set offer_data = component.offer %}
        {% elseif offer is defined %}
            {% set offer_data = offer %}
        {% endif %}

        {# تعيين القيم من البيانات المسترجعة إذا كانت متوفرة #}
        {% if offer_data is not null %}
            {% if offer_data.img is defined %}
                {% set offer_img = offer_data.img %}
            {% endif %}
            {% if offer_data.url is defined %}
                {% set offer_url = offer_data.url %}
            {% endif %}
        {% endif %}

        {# Banner Image #}
        {% if offer_img %}
            <img src="{{ offer_img }}" alt="Special Offer" class="banner-image" loading="lazy">
        {% else %}
            <img src="{{ 'images/placeholder.png'|asset }}" alt="Special Offer" class="banner-image" loading="lazy">
        {% endif %}

        {# Overlay #}
        <div class="overlay"></div>
        
        {# Particles effect #}
        <div class="particles">
            {% for i in 1..15 %}
                <div class="particle" style="left: {{ random(100) }}%; top: {{ random(100) }}%; animation-delay: {{ random(5) }}s; animation-duration: {{ 10 + random(10) }}s;"></div>
            {% endfor %}
        </div>

        {# Content #}
        <div class="offer-content">
            <h3 class="offer-title">العرض ينتهي خلال:</h3>

            <!-- العد التنازلي بتصميم إبداعي -->
            <div id="countdown-timer-{{ unique_id }}" class="countdown-timer">
                <div class="countdown-box">
                    <div class="bubble-decoration bubble-top-right"></div>
                    <div class="bubble-decoration bubble-bottom-left"></div>
                    <span id="days-{{ unique_id }}" class="countdown-value">--</span>
                    <span class="countdown-label">أيام</span>
                </div>
                <div class="countdown-box">
                    <div class="bubble-decoration bubble-top-right-medium"></div>
                    <div class="bubble-decoration bubble-bottom-left-large"></div>
                    <span id="hours-{{ unique_id }}" class="countdown-value">--</span>
                    <span class="countdown-label">ساعات</span>
                </div>
                <div class="countdown-box">
                    <div class="bubble-decoration bubble-top-right-small"></div>
                    <div class="bubble-decoration bubble-bottom-left-medium"></div>
                    <span id="minutes-{{ unique_id }}" class="countdown-value">--</span>
                    <span class="countdown-label">دقائق</span>
                </div>
                <div class="countdown-box">
                    <div class="bubble-decoration bubble-top-right"></div>
                    <div class="bubble-decoration bubble-bottom-left"></div>
                    <span id="seconds-{{ unique_id }}" class="countdown-value">--</span>
                    <span class="countdown-label">ثواني</span>
                </div>
            </div>

            <!-- شارة نهاية العرض (مخفية بشكل افتراضي) -->
            <div id="offer-ended-badge-{{ unique_id }}" class="offer-ended-badge">
                <div class="badge-pattern"></div>
                <span class="badge-text">نهاية<br>العرض</span>
            </div>

            <a href="{{ offer_url }}" class="offer-cta-button">
                <span class="button-text">اضغط للعرض</span>
                <span class="bubble-decoration bubble-top-right"></span>
                <span class="bubble-decoration bubble-bottom-left"></span>
            </a>
        </div>
    </div>
</div>

<!-- سكريبت العد التنازلي المحسن -->
<script>
    // استخدام requestAnimationFrame لتحسين الأداء
    requestAnimationFrame(function() {
        // إنشاء معرف فريد لهذا المكون
        var timerID = "{{ unique_id|escape('js') }}";

        // تحديد العناصر الخاصة بهذا المكون باستخدام المعرف الفريد
        var elements = {
            days: document.getElementById("days-" + timerID),
            hours: document.getElementById("hours-" + timerID),
            minutes: document.getElementById("minutes-" + timerID),
            seconds: document.getElementById("seconds-" + timerID),
            badge: document.getElementById("offer-ended-badge-" + timerID),
            countdown: document.getElementById("countdown-timer-" + timerID)
        };

        // التأكد من وجود العناصر الأساسية قبل المتابعة
        if (!elements.days || !elements.hours || !elements.minutes || !elements.seconds) {
            console.error("لم يتم العثور على عناصر التايمر للمكون: " + timerID);
            return;
        }

        // تاريخ انتهاء العرض (بعد عدد أيام من الآن)
        var timerConfig = window.offerBannerConfig['{{ unique_id }}'];
        var endDate = new Date();
        endDate.setDate(endDate.getDate() + timerConfig.days);

        // تخزين القيم السابقة لتجنب التحديثات غير الضرورية
        var previousValues = {
            days: null,
            hours: null,
            minutes: null,
            seconds: null
        };

        // متغير للتحكم في معدل التحديث
        var lastUpdateTime = 0;
        var updateInterval = 1000; // تحديث كل ثانية

        // دالة تحديث العد التنازلي
        function updateCountdown() {
            // التحقق من الوقت المنقضي منذ آخر تحديث
            var now = Date.now();
            var elapsed = now - lastUpdateTime;

            // تحديث فقط إذا مر الوقت المحدد
            if (elapsed >= updateInterval) {
                lastUpdateTime = now;

                // حساب الوقت المتبقي
                var distance = endDate - now;

                if (distance < 0) {
                    // إذا انتهى العد التنازلي
                    handleCountdownEnd();
                    return;
                }

                // حسابات الوقت
                var timeValues = {
                    days: Math.floor(distance / (1000 * 60 * 60 * 24)),
                    hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
                    minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
                    seconds: Math.floor((distance % (1000 * 60)) / 1000)
                };

                // تحديث العناصر فقط إذا تغيرت القيم
                for (var key in timeValues) {
                    if (previousValues[key] !== timeValues[key]) {
                        elements[key].textContent = timeValues[key];
                        previousValues[key] = timeValues[key];

                        // إضافة تأثير بسيط عند تغيير القيمة
                        animateValueChange(elements[key]);
                    }
                }
            }

            // استدعاء الدالة مرة أخرى في الإطار التالي
            requestAnimationFrame(updateCountdown);
        }

        // دالة لإضافة تأثير بسيط عند تغيير القيمة - تحسين الأداء
        function animateValueChange(element) {
            // التحقق من تفضيلات المستخدم لتقليل الحركة
            if (document.documentElement.classList.contains('reduced-motion')) {
                return; // عدم تطبيق الرسوم المتحركة إذا كان المستخدم يفضل تقليل الحركة
            }

            // استخدام Web Animation API بدلاً من setTimeout للحصول على أداء أفضل
            element.animate([
                { transform: 'scale(1)', textShadow: '0 0 10px {{ bg_color }}, 0 0 5px {{ bg_color }}' },
                { transform: 'scale(1.1)', textShadow: '0 0 20px {{ bg_color }}, 0 0 10px {{ bg_color }}' },
                { transform: 'scale(1)', textShadow: '0 0 10px {{ bg_color }}, 0 0 5px {{ bg_color }}' }
            ], {
                duration: 300,
                easing: 'cubic-bezier(0.2, 0, 0.2, 1)'
            });
        }

        // دالة للتعامل مع انتهاء العد التنازلي
        function handleCountdownEnd() {
            // تعيين جميع القيم إلى صفر
            elements.days.textContent = "0";
            elements.hours.textContent = "0";
            elements.minutes.textContent = "0";
            elements.seconds.textContent = "0";

            // إظهار شارة نهاية العرض
            if (elements.badge) {
                elements.badge.style.display = "flex";

                // استخدام Web Animation API مع تحسين الأداء
                requestAnimationFrame(function() {
                    elements.badge.animate([
                        { transform: 'scale(0) rotate(0deg)', opacity: 0, boxShadow: '0 0 0 {{ bg_color }}00' },
                        { transform: 'scale(1.2) rotate(15deg)', opacity: 1, boxShadow: '0 0 30px {{ bg_color }}80' },
                        { transform: 'scale(1) rotate(15deg)', opacity: 1, boxShadow: '0 0 15px {{ bg_color }}60' }
                    ], {
                        duration: 800,
                        easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                        fill: 'forwards'
                    });

                    // إضافة تأثير اهتزاز للشارة بعد ظهورها
                    setTimeout(function() {
                        elements.badge.animate([
                            { transform: 'rotate(15deg)' },
                            { transform: 'rotate(20deg)' },
                            { transform: 'rotate(10deg)' },
                            { transform: 'rotate(15deg)' }
                        ], {
                            duration: 500,
                            iterations: 2,
                            easing: 'ease-in-out'
                        });
                    }, 1000);
                });
            }

            // إضافة تأثير للعد التنازلي
            if (elements.countdown) {
                requestAnimationFrame(function() {
                    elements.countdown.animate([
                        { opacity: 1, filter: 'blur(0px)' },
                        { opacity: 0.5, filter: 'blur(2px)' },
                        { opacity: 0.8, filter: 'blur(0px)' }
                    ], {
                        duration: 1500,
                        easing: 'ease-in-out'
                    });
                });
            }
        }

        // بدء العد التنازلي باستخدام requestAnimationFrame
        requestAnimationFrame(updateCountdown);
    });
</script>