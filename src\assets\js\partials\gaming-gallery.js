/**
 * Gaming Gallery Slider
 * Handles circular image gallery with slider functionality for the gaming theme
 */

export function initGamingGallery() {
    document.addEventListener('DOMContentLoaded', function() {
        const slider = document.getElementById('gaming-gallery-slider');
        if (!slider || !slider.classList.contains('with-slider')) return;
        
        const track = slider.querySelector('.gaming-gallery-track');
        const items = slider.querySelectorAll('.gaming-gallery-item');
        const dots = slider.querySelectorAll('.gaming-gallery-dot');
        const prevBtn = slider.querySelector('.gaming-gallery-prev-btn');
        const nextBtn = slider.querySelector('.gaming-gallery-next-btn');
        
        let currentIndex = 0;
        let itemsPerSlide = getItemsPerSlide();
        let totalSlides = Math.ceil(items.length / itemsPerSlide);
        
        // Initially apply the right transform
        updateSlider();
        
        // Handle window resize
        window.addEventListener('resize', function() {
            itemsPerSlide = getItemsPerSlide();
            totalSlides = Math.ceil(items.length / itemsPerSlide);
            updateSlider();
        });
        
        // Next button
        nextBtn.addEventListener('click', function() {
            currentIndex = (currentIndex + 1) % totalSlides;
            updateSlider();
        });
        
        // Previous button
        prevBtn.addEventListener('click', function() {
            currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
            updateSlider();
        });
        
        // Dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', function() {
                currentIndex = index;
                updateSlider();
            });
        });
        
        // Auto-sliding
        let autoSlideInterval = setInterval(function() {
            currentIndex = (currentIndex + 1) % totalSlides;
            updateSlider();
        }, 5000);
        
        // Pause auto-sliding on hover
        slider.addEventListener('mouseenter', function() {
            clearInterval(autoSlideInterval);
        });
        
        slider.addEventListener('mouseleave', function() {
            autoSlideInterval = setInterval(function() {
                currentIndex = (currentIndex + 1) % totalSlides;
                updateSlider();
            }, 5000);
        });
        
        // Add glowing circuit board effect to background
        const section = slider.closest('.gaming-gallery-section');
        if (section) {
            // Add particles
            for (let i = 0; i < 5; i++) {
                createParticle(section);
            }
            
            // Add circuit board background
            const circuitBg = document.createElement('div');
            circuitBg.className = 'gaming-gallery-circuit-bg';
            section.appendChild(circuitBg);
        }
    });
    
    // Helper functions
    function getItemsPerSlide() {
        if (window.innerWidth <= 576) return 1;
        if (window.innerWidth <= 991) return 2;
        return 3;
    }
    
    function updateSlider() {
        const slider = document.getElementById('gaming-gallery-slider');
        const track = slider.querySelector('.gaming-gallery-track');
        const items = slider.querySelectorAll('.gaming-gallery-item');
        const dots = slider.querySelectorAll('.gaming-gallery-dot');
        const currentIndex = parseInt(track.getAttribute('data-index') || '0');
        
        const itemWidth = items[0].offsetWidth + parseInt(window.getComputedStyle(items[0]).marginRight) * 2;
        const translateX = -currentIndex * itemWidth * getItemsPerSlide();
        track.style.transform = `translateX(${translateX}px)`;
        track.setAttribute('data-index', currentIndex);
        
        // Update active dot
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentIndex);
        });
    }
    
    function createParticle(parent) {
        const particle = document.createElement('div');
        
        // Random position
        const posX = Math.random() * 90 + 5; // 5% to 95%
        const posY = Math.random() * 90 + 5; // 5% to 95%
        
        // Random size (2-4px)
        const size = 2 + Math.random() * 2;
        
        // Random animation duration (8-15s)
        const duration = 8 + Math.random() * 7;
        
        // Set styles
        particle.style.cssText = `
            position: absolute;
            top: ${posY}%;
            left: ${posX}%;
            width: ${size}px;
            height: ${size}px;
            border-radius: 50%;
            background-color: rgba(29, 233, 182, 0.8);
            box-shadow: 0 0 10px 2px rgba(29, 233, 182, 0.8);
            z-index: 1;
            animation: galleryParticleFloat ${duration}s infinite ease-in-out ${Math.random() * 5}s;
        `;
        
        // Add to parent
        parent.appendChild(particle);
    }
} 