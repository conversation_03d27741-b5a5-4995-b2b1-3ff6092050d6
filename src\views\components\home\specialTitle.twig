{#
| Variable                      | Type      | Description                                                         |
|-------------------------------|-----------|---------------------------------------------------------------------|
| component.title               | string    | Title text from admin panel                                         |
#}

<section class="s-block special-title-component">
    <div class="container">
        <div class="special-title-wrapper">
            <div class="special-title-dust-container">
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
                <div class="dust-particle"></div>
            </div>
            <h1 class="special-title-text">{{ component.title ? component.title : 'ادخل عنوان او نص' }}</h1>
            <div class="special-title-decoration">
                <span class="special-title-decoration__line"></span>
                <span class="special-title-decoration__icon">
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="1" y="1" width="30" height="30" rx="4" stroke="currentColor" stroke-width="1.5" stroke-dasharray="4 2"/>
                        <circle cx="16" cy="16" r="8" stroke="currentColor" stroke-width="1.5"/>
                        <circle cx="16" cy="16" r="3" fill="currentColor"/>
                        <path d="M5 16H10" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M22 16H27" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M16 5V10" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M16 22V27" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M7 7L11 11" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M21 21L25 25" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M7 25L11 21" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M21 11L25 7" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                </span>
                <span class="special-title-decoration__line"></span>
            </div>
            <div class="special-title-circuit-decoration">
                <div class="circuit-line line-left"></div>
                <div class="circuit-line line-right"></div>
                <div class="circuit-dot dot-1"></div>
                <div class="circuit-dot dot-2"></div>
                <div class="circuit-dot dot-3"></div>
                <div class="circuit-dot dot-4"></div>
                <div class="circuit-corner top-left"></div>
                <div class="circuit-corner top-right"></div>
                <div class="circuit-corner bottom-left"></div>
                <div class="circuit-corner bottom-right"></div>
            </div>
        </div>
    </div>

    <style>
        /* Special Title Component Styling */
        .special-title-component {
            padding: 3rem 0;
            position: relative;
            overflow: hidden;
            /* Background removed as requested */
        }

        :root {
            --color-primary-rgb: 29, 233, 182; /* Cyan RGB */
            --color-primary: #1DE9B6; /* Explicit cyan color */
        }

        .special-title-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .special-title-text {
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 1rem;
            position: relative;
            display: inline-block;
            padding: 0 1rem;
            text-align: center;
            z-index: 2;
            text-shadow: 0 0 10px var(--color-primary), 0 0 20px var(--color-primary), 0 0 30px var(--color-primary);
            transition: all 0.5s ease;
            letter-spacing: 1px;
        }

        .special-title-text:hover {
            transform: translateY(-2px) scale(1.03);
            text-shadow: 0 0 15px var(--color-primary), 0 0 30px var(--color-primary), 0 0 45px var(--color-primary);
        }

        /* Title decoration styling */
        .special-title-decoration {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 0.5rem;
            width: 100%;
            max-width: 500px;
            z-index: 2;
        }

        .special-title-decoration__line {
            height: 2px;
            width: 100px;
            background: linear-gradient(90deg, rgba(0,0,0,0) 0%, var(--color-primary) 100%);
            display: inline-block;
            box-shadow: 0 0 10px var(--color-primary);
            position: relative;
            overflow: hidden;
        }

        .special-title-decoration__line:after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 40%;
            height: 100%;
            background-color: rgba(255,255,255,0.4);
            transform: skewX(-25deg);
            animation: line-shine 3s infinite;
        }

        .special-title-decoration__line:last-child {
            background: linear-gradient(90deg, var(--color-primary) 0%, rgba(0,0,0,0) 100%);
        }

        .special-title-decoration__line:last-child:after {
            animation: line-shine 3s infinite 1.5s;
        }

        @keyframes line-shine {
            0% {
                left: -100%;
            }
            50%, 100% {
                left: 150%;
            }
        }

        .special-title-decoration__icon {
            margin: 0 15px;
            color: var(--color-primary);
            opacity: 0.9;
            display: flex;
            align-items: center;
            transform: scale(1.2);
            filter: drop-shadow(0 0 5px var(--color-primary));
            animation: pulse-rotate 4s infinite ease-in-out;
        }

        @keyframes pulse-rotate {
            0% {
                transform: scale(1.2) rotate(0deg);
                filter: drop-shadow(0 0 3px var(--color-primary));
            }
            50% {
                transform: scale(1.3) rotate(180deg);
                filter: drop-shadow(0 0 8px var(--color-primary));
            }
            100% {
                transform: scale(1.2) rotate(360deg);
                filter: drop-shadow(0 0 3px var(--color-primary));
            }
        }

        /* Circuit-style decoration */
        .special-title-circuit-decoration {
            position: absolute;
            width: 100%;
            max-width: 600px;
            height: 100%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
            pointer-events: none;
        }

        .circuit-line {
            position: absolute;
            height: 2px;
            background-color: var(--color-primary);
            opacity: 0.3;
            box-shadow: 0 0 8px var(--color-primary);
        }

        .line-left {
            width: 20%;
            top: 45%;
            left: 0;
            animation: line-pulse 3s infinite;
        }

        .line-right {
            width: 20%;
            top: 45%;
            right: 0;
            animation: line-pulse 3s infinite 1.5s;
        }

        .circuit-dot {
            position: absolute;
            width: 6px;
            height: 6px;
            background-color: var(--color-primary);
            border-radius: 50%;
            opacity: 0.7;
            box-shadow: 0 0 8px var(--color-primary);
            animation: dot-pulse 3s infinite;
        }

        .dot-1 {
            top: 45%;
            left: 20%;
            animation-delay: 0.3s;
        }

        .dot-2 {
            top: 45%;
            right: 20%;
            animation-delay: 1.8s;
        }

        .dot-3 {
            top: 20%;
            left: 50%;
            animation-delay: 0.9s;
        }

        .dot-4 {
            bottom: 20%;
            left: 50%;
            animation-delay: 2.1s;
        }

        .circuit-corner {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid var(--color-primary);
            opacity: 0.3;
            box-shadow: 0 0 8px var(--color-primary);
        }

        .top-left {
            top: 0;
            left: 0;
            border-right: none;
            border-bottom: none;
            animation: corner-pulse 3s infinite 0.2s;
        }

        .top-right {
            top: 0;
            right: 0;
            border-left: none;
            border-bottom: none;
            animation: corner-pulse 3s infinite 1.2s;
        }

        .bottom-left {
            bottom: 0;
            left: 0;
            border-right: none;
            border-top: none;
            animation: corner-pulse 3s infinite 1.7s;
        }

        .bottom-right {
            bottom: 0;
            right: 0;
            border-left: none;
            border-top: none;
            animation: corner-pulse 3s infinite 0.7s;
        }

        @keyframes line-pulse {
            0% {
                opacity: 0.3;
                box-shadow: 0 0 5px var(--color-primary);
            }
            50% {
                opacity: 0.8;
                box-shadow: 0 0 15px var(--color-primary);
            }
            100% {
                opacity: 0.3;
                box-shadow: 0 0 5px var(--color-primary);
            }
        }

        @keyframes dot-pulse {
            0% {
                transform: scale(1);
                opacity: 0.3;
                box-shadow: 0 0 5px var(--color-primary);
            }
            50% {
                transform: scale(1.5);
                opacity: 0.8;
                box-shadow: 0 0 15px var(--color-primary);
            }
            100% {
                transform: scale(1);
                opacity: 0.3;
                box-shadow: 0 0 5px var(--color-primary);
            }
        }

        @keyframes corner-pulse {
            0% {
                opacity: 0.3;
                box-shadow: 0 0 5px var(--color-primary);
            }
            50% {
                opacity: 0.6;
                box-shadow: 0 0 10px var(--color-primary);
            }
            100% {
                opacity: 0.3;
                box-shadow: 0 0 5px var(--color-primary);
            }
        }

        /* Digital Particle Effects */
        .special-title-dust-container {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 1;
        }

        .dust-particle {
            position: absolute;
            background: transparent;
            border-radius: 4px;
            opacity: 0;
            transform-origin: center;
            box-shadow: 0 0 8px var(--color-primary);
        }

        /* Various particle types */
        .dust-particle:nth-child(3n+1) {
            width: 8px;
            height: 2px;
            background: var(--color-primary);
        }

        .dust-particle:nth-child(3n+2) {
            width: 2px;
            height: 8px;
            background: var(--color-primary);
        }

        .dust-particle:nth-child(3n) {
            width: 4px;
            height: 4px;
            background: transparent;
            border: 1px solid var(--color-primary);
        }

        /* Custom positions and animations */
        .dust-particle:nth-child(1) { top: 15%; left: 30%; animation: digital-float 3s infinite 0.2s, pulse-glow 2s infinite 0.1s; }
        .dust-particle:nth-child(2) { top: 25%; left: 65%; animation: digital-float 2.7s infinite 0.5s, pulse-glow 2s infinite 0.3s; }
        .dust-particle:nth-child(3) { top: 35%; left: 25%; animation: digital-float 3.5s infinite 0.7s, pulse-glow 2s infinite 0.2s; }
        .dust-particle:nth-child(4) { top: 45%; left: 70%; animation: digital-float 2.8s infinite 0.3s, pulse-glow 2s infinite 0.7s; }
        .dust-particle:nth-child(5) { top: 55%; left: 35%; animation: digital-float 3.2s infinite 0.6s, pulse-glow 2s infinite 0.4s; }
        .dust-particle:nth-child(6) { top: 65%; left: 75%; animation: digital-float 3s infinite 0.9s, pulse-glow 2s infinite 0.1s; }
        .dust-particle:nth-child(7) { top: 20%; left: 80%; animation: digital-float 2.5s infinite 0.4s, pulse-glow 2s infinite 0.5s; }
        .dust-particle:nth-child(8) { top: 30%; left: 20%; animation: digital-float 3.3s infinite 0.8s, pulse-glow 2s infinite 0.3s; }
        .dust-particle:nth-child(9) { top: 40%; left: 85%; animation: digital-float 2.9s infinite 0.1s, pulse-glow 2s infinite 0.8s; }
        .dust-particle:nth-child(10) { top: 50%; left: 15%; animation: digital-float 3.1s infinite 0.5s, pulse-glow 2s infinite 0.2s; }
        .dust-particle:nth-child(11) { top: 60%; left: 90%; animation: digital-float 2.6s infinite 0.7s, pulse-glow 2s infinite 0.6s; }
        .dust-particle:nth-child(12) { top: 70%; left: 10%; animation: digital-float 3.4s infinite 0.2s, pulse-glow 2s infinite 0.9s; }

        /* Add more particles with different types */
        .special-title-dust-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: radial-gradient(circle at center, var(--color-primary) 1px, transparent 1px);
            background-size: 40px 40px;
            opacity: 0.1;
            z-index: -1;
            animation: sparkle 4s infinite linear;
        }

        /* Animation for the decoration */
        .special-title-wrapper {
            animation: fadeIn 1.5s ease-in-out;
        }

        .special-title-decoration__line {
            animation: expandLine 1.5s ease-in-out, glowPulse 2s infinite alternate;
        }

        .special-title-decoration__icon {
            animation: pulse 2s infinite ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes expandLine {
            from {
                width: 0;
            }
            to {
                width: 100px;
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                filter: drop-shadow(0 0 3px var(--color-primary));
            }
            50% {
                transform: scale(1.2);
                filter: drop-shadow(0 0 8px var(--color-primary));
            }
            100% {
                transform: scale(1);
                filter: drop-shadow(0 0 3px var(--color-primary));
            }
        }

        @keyframes glowPulse {
            0% {
                box-shadow: 0 0 5px var(--color-primary);
            }
            100% {
                box-shadow: 0 0 15px var(--color-primary), 0 0 20px var(--color-primary);
            }
        }

        @keyframes digital-float {
            0% {
                transform: translate(0, 0) rotate(0deg);
                opacity: 0;
            }
            25% {
                opacity: 0.8;
            }
            75% {
                opacity: 0.8;
            }
            100% {
                transform: translate(var(--tx, 20px), var(--ty, -20px)) rotate(var(--rot, 90deg));
                opacity: 0;
            }
        }

        @keyframes pulse-glow {
            0% {
                box-shadow: 0 0 5px var(--color-primary);
                opacity: 0.2;
            }
            50% {
                box-shadow: 0 0 12px var(--color-primary);
                opacity: 0.9;
            }
            100% {
                box-shadow: 0 0 5px var(--color-primary);
                opacity: 0.2;
            }
        }

        @keyframes sparkle {
            0% {
                opacity: 0.05;
                transform: translateY(0);
            }
            50% {
                opacity: 0.15;
            }
            100% {
                opacity: 0.05;
                transform: translateY(-10px);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .special-title-text {
                font-size: 2rem;
            }

            .special-title-decoration__line {
                width: 70px;
            }
        }

        @media (max-width: 480px) {
            .special-title-text {
                font-size: 1.5rem;
            }

            .special-title-decoration__line {
                width: 50px;
            }

            .special-title-decoration__icon {
                transform: scale(1);
                margin: 0 10px;
            }
        }
    </style>
</section>