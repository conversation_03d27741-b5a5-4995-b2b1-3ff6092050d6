.store-footer {
  @apply relative bg-darker text-white mt-8 sm:mt-16;

  a{
    @apply transition-opacity hover:opacity-75;
  }

  &__inner{
    @apply bg-dark py-8 lg:py-16 border-b border-dashed border-b-green-500/10;
  }  

  h3{
    @apply text-lg font-bold mb-3 lg:mb-5;
  }

  .social-link {
    @apply border rounded-full w-8 h-8 justify-center flex items-center hover:opacity-75 transition duration-300 text-sm
  }

  .contact-social{
    @apply mt-5 pt-5 border-t border-white/10;

    ul {
      @apply mb-0;
    }
  }

  .footer-is-light & {
    @apply bg-gray-50 text-gray-700;

    .store-footer__inner {
      @apply bg-gray-100 border-b-green-500/10;
    }

    .social-link {
      @apply border-gray-300;
    }

    .contact-social{
      @apply border-gray-100;
    }
  }
  
  /* Gaming Theme Footer Styling */
  .gaming-theme & {
    @apply bg-[#0a0a15] text-gray-200 mt-0;
    position: relative;
    
    /* Create a top glow effect */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(111, 76, 255, 0.3) 20%, 
        rgba(111, 76, 255, 0.5) 50%,
        rgba(111, 76, 255, 0.3) 80%,
        transparent 100%);
      box-shadow: 0 0 15px rgba(111, 76, 255, 0.5);
    }
    
    /* Add animated glow to the footer */
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 30%;
      height: 1px;
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(111, 76, 255, 0.5) 50%,
        transparent 100%);
      animation: footerGlow 8s infinite ease-in-out;
      box-shadow: 0 0 20px 5px rgba(111, 76, 255, 0.4);
    }
    
    @keyframes footerGlow {
      0% { left: -30%; }
      100% { left: 100%; }
    }
    
    a {
      @apply transition-all hover:opacity-100 hover:text-white;
      text-shadow: 0 0 8px rgba(111, 76, 255, 0);
      
      &:hover {
        text-shadow: 0 0 8px rgba(111, 76, 255, 0.5);
        transform: translateX(3px);
      }
    }
    
    &__inner {
      @apply bg-[#0a0a18] border-b border-purple-500/20;
      background-image: linear-gradient(to bottom, 
        rgb(0, 0, 0) 0%, 
        rgb(41, 1, 23) 100%);
      box-shadow: 0 -10px 30px rgba(0, 0, 0, 0) inset;
    }
    
    h3 {
      @apply text-white font-bold;
      text-shadow: 0 0 10px rgba(111, 76, 255, 0.5);
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 40px;
        height: 2px;
        background: linear-gradient(90deg, rgba(111, 76, 255, 0.8), transparent);
        box-shadow: 0 0 10px rgba(111, 76, 255, 0.5);
      }
    }
    
    .social-link {
      @apply border-purple-500/30 text-gray-300 hover:text-white hover:border-purple-500;
      transition: all 0.3s ease;
      box-shadow: 0 0 10px rgba(111, 76, 255, 0.1);
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 0 15px rgba(111, 76, 255, 0.3);
      }
    }
    
    .contact-social {
      @apply border-t border-purple-500/20;
    }
    
    /* Style payment methods section */
    salla-payments {
      .s-payments-wrapper {
        filter: grayscale(70%);
        opacity: 0.7;
        transition: all 0.3s ease;
        
        &:hover {
          filter: grayscale(0%);
          opacity: 1;
        }
      }
    }
  }
}


.copyright-text p{
  @apply text-gray-700;
  
  .gaming-theme & {
    @apply text-gray-400;
  }
}

/* Add a pulse glow effect to app icons in footer */
.gaming-theme salla-apps-icons {
  img {
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 0 rgba(111, 76, 255, 0));
    
    &:hover {
      transform: scale(1.1);
      filter: drop-shadow(0 0 5px rgba(111, 76, 255, 0.7));
    }
  }
}