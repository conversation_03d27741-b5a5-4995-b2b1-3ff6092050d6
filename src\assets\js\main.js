// استيراد العناصر المخصصة
import './partials/category-search.js';
import './partials/category-filter.js';

/**
 * تحسينات الإصدار 1.3.0:
 * - إصلاح خطأ SourceValue بإرسال قيمة عددية
 * - معالجة خطأ نقاط نهاية API غير المتوفرة (410 Gone)
 * - تبسيط استراتيجية البحث للتركيز على الطرق الأكثر موثوقية
 * - تحسين البحث المحلي بتنقية النتائج بناءً على صلتها بالفئة
 * - دعم مختلف إصدارات منصة سلة
 */

// إضافة العناصر المخصصة إلى الهيدر بعد تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
  console.log('تم تحميل مكونات البحث والفلترة المحسّنة - الإصدار 1.3.0');
  console.log('تم إصلاح مشكلة ظهور المنتجات في الفئات غير المناسبة ومعالجة أخطاء API غير المتوفر');
  
  // إضافة عنصر البحث بالفئات
  const headerSearch = document.querySelector('.header .S-search');
  if (headerSearch) {
    // استبدال عنصر البحث الافتراضي بعنصر البحث المخصص
    const categorySearch = document.createElement('category-search');
    headerSearch.innerHTML = '';
    headerSearch.appendChild(categorySearch);
  }
  
  // إضافة عنصر فلترة الفئات
  const headerElement = document.querySelector('.header .container') || document.querySelector('.header');
  if (headerElement) {
    const categoryFilter = document.createElement('category-filter');
    
    // إذا كان هناك قائمة في الهيدر، نضيف العنصر بعدها
    const headerNav = document.querySelector('.header .main-nav');
    if (headerNav) {
      headerNav.parentNode.insertBefore(categoryFilter, headerNav.nextSibling);
    } else {
      // إضافة العنصر في نهاية الهيدر
      headerElement.appendChild(categoryFilter);
    }
  }
}); 