import BasePage from '../base-page';
class ProductCard extends HTMLElement {
  constructor(){
    super()
  }
  
  connectedCallback(){
    // Parse product data
    this.product = this.product || JSON.parse(this.getAttribute('product')); 

    if (window.app?.status === 'ready') {
      this.onReady();
    } else {
      document.addEventListener('theme::ready', () => this.onReady() )
    }
  }

  onReady(){
      this.fitImageHeight = salla.config.get('store.settings.product.fit_type');
      this.placeholder = salla.url.asset(salla.config.get('theme.settings.placeholder'));
      this.getProps()

	  this.source = salla.config.get("page.slug");
      // If the card is in the landing page, hide the add button and show the quantity
	  if (this.source == "landing-page") {
	  	this.hideAddBtn = true;
	  	this.showQuantity = window.showQuantity;
	  }

      salla.lang.onLoaded(() => {
        // Language
        this.remained = salla.lang.get('pages.products.remained');
        this.donationAmount = salla.lang.get('pages.products.donation_amount');
        this.startingPrice = salla.lang.get('pages.products.starting_price');
        this.addToCart = salla.lang.get('pages.cart.add_to_cart');
        this.outOfStock = salla.lang.get('pages.products.out_of_stock');

        // re-render to update translations
        this.render();
      })
      
      this.render()
  }

  initCircleBar() {
    let qty = this.product.quantity,
      total = this.product.quantity > 100 ? this.product.quantity * 2 : 100,
      roundPercent = (qty / total) * 100,
      bar = this.querySelector('.s-product-card-content-pie-svg-bar'),
      strokeDashOffsetValue = 100 - roundPercent;
    bar.style.strokeDashoffset = strokeDashOffsetValue;
  }

  formatDate(date) {
    let d = new Date(date);
    return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`;
  } 

  getProductBadge() {
    if (this.product.promotion_title) {
      return `<div class="s-product-card-promotion-title">${this.product.promotion_title}</div>`
    }
    if (this.showQuantity && this.product?.quantity) {
      return `<div
        class="s-product-card-quantity">${this.remained} ${salla.helpers.number(this.product?.quantity)}</div>`
    }
    if (this.showQuantity && this.product?.is_out_of_stock) {
      return `<div class="s-product-card-out-badge">${this.outOfStock}</div>`
    }
    return '';
  }

  getPriceFormat(price) {
    if (!price || price == 0) {
      return salla.config.get('store.settings.product.show_price_as_dash')?'-':'';
    }

    return salla.money(price);
  }

  getProductPrice() {
    let price = '';
    if (this.product.is_on_sale) {
      price = `<div class="s-product-card-sale-price">
                <h4>${this.getPriceFormat(this.product.sale_price)}</h4>
                <span>${this.getPriceFormat(this.product?.regular_price)}</span>
              </div>`;
    }
    else if (this.product.starting_price) {
      price = `<div class="s-product-card-starting-price">
                  <p>${this.startingPrice}</p>
                  <h4> ${this.getPriceFormat(this.product?.starting_price)} </h4>
              </div>`
    }
    else{
      price = `<h4 class="s-product-card-price">${this.getPriceFormat(this.product?.price)}</h4>`
    }

    return price;
  }

  getAddButtonLabel() {
    if (this.product.status === 'sale' && this.product.type === 'booking') {
      return salla.lang.get('pages.cart.book_now'); 
    }

    if (this.product.status === 'sale') {
      return salla.lang.get('pages.cart.add_to_cart');
    }

    if (this.product.type !== 'donating') {
      return salla.lang.get('pages.products.out_of_stock');
    }

    // donating
    return salla.lang.get('pages.products.donation_exceed');
  }

  getProps(){

    /**
     *  Horizontal card.
     */
    this.horizontal = this.hasAttribute('horizontal');
  
    /**
     *  Support shadow on hover.
     */
    this.shadowOnHover = this.hasAttribute('shadowOnHover');
  
    /**
     *  Hide add to cart button.
     */
    this.hideAddBtn = this.hasAttribute('hideAddBtn');
  
    /**
     *  Full image card.
     */
    this.fullImage = this.hasAttribute('fullImage');
  
    /**
     *  Minimal card.
     */
    this.minimal = this.hasAttribute('minimal');
  
    /**
     *  Special card.
     */
    this.isSpecial = this.hasAttribute('isSpecial');
  
    /**
     *  Show quantity.
     */
    this.showQuantity = this.hasAttribute('showQuantity');
  }

  render(){
    this.classList.add('s-product-card-entry', 'gaming-product-card'); 
    this.setAttribute('id', this.product.id);
    !this.horizontal && !this.fullImage && !this.minimal? this.classList.add('s-product-card-vertical') : '';
    this.horizontal && !this.fullImage && !this.minimal? this.classList.add('s-product-card-horizontal') : '';
    this.fitImageHeight && !this.isSpecial && !this.fullImage && !this.minimal? this.classList.add('s-product-card-fit-height') : '';
    this.isSpecial? this.classList.add('s-product-card-special') : '';
    this.fullImage? this.classList.add('s-product-card-full-image') : '';
    this.minimal? this.classList.add('s-product-card-minimal') : '';
    this.product?.donation?  this.classList.add('s-product-card-donation') : '';
    this.shadowOnHover?  this.classList.add('s-product-card-shadow') : '';
    this.product?.is_out_of_stock?  this.classList.add('s-product-card-out-of-stock') : '';
    this.isInWishlist = !salla.config.isGuest() && salla.storage.get('salla::wishlist', []).includes(this.product.id);
    this.innerHTML = `
        <div class="${!this.fullImage ? 's-product-card-image gaming-product-image' : 's-product-card-image-full gaming-product-image-full'}">
          <div class="gaming-product-glow-border"></div>
          <a href="${this.product?.url}">
            <img class="s-product-card-image-${salla.url.is_placeholder(this.product?.image?.url)
              ? 'contain'
              : this.fitImageHeight
                ? this.fitImageHeight
                : 'cover'} lazy"
              src=${this.placeholder}
              alt=${this.product?.image?.alt}
              data-src=${this.product?.image?.url || this.product?.thumbnail}
            />
            ${!this.fullImage && !this.minimal ? this.getProductBadge() : ''}
          </a>
          ${this.fullImage ? `<a href="${this.product?.url}" aria-label=${this.product.name} class="s-product-card-overlay gaming-product-overlay"></a>`:''}
          ${!this.horizontal && !this.fullImage ?
            `<salla-button
              shape="icon"
              fill="outline"
              color="light"
              name="product-name-${this.product.id}"
              aria-label="Add or remove to wishlist"
              class="s-product-card-wishlist-btn gaming-wishlist-btn animated ${this.isInWishlist ? 's-product-card-wishlist-added pulse-anime' : 'not-added un-favorited'}"
              onclick="salla.wishlist.toggle(${this.product.id})"
              data-id="${this.product.id}">
              <i class="sicon-heart"></i>
            </salla-button>` : ``
          }
        </div>
        <div class="s-product-card-content gaming-product-content">
          ${this.isSpecial && this.product?.quantity ?
            `<div class="s-product-card-content-pie gaming-product-pie">
              <span>
                <b>${salla.helpers.number(this.product?.quantity)}</b>
                ${this.remained}
              </span>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="-2 -1 36 34" class="s-product-card-content-pie-svg">
                <circle cx="16" cy="16" r="15.9155" class="s-product-card-content-pie-svg-base" />
                <circle cx="16" cy="16" r="15.9155" class="s-product-card-content-pie-svg-bar" />
              </svg>
            </div>`
            : ``}

          <div class="s-product-card-content-main ${this.isSpecial ? 's-product-card-content-extra-padding' : ''} gaming-product-main">
            <h3 class="s-product-card-content-title gaming-product-title">
              <a href="${this.product?.url}">${this.product?.name}</a>
            </h3>

            ${this.product?.subtitle && !this.minimal ?
              `<p class="s-product-card-content-subtitle gaming-product-subtitle opacity-80">${this.product?.subtitle}</p>`
              : ``}
          </div>
          ${this.product?.donation && !this.minimal && !this.fullImage ?
          `<salla-progress-bar donation=${JSON.stringify(this.product?.donation)}></salla-progress-bar>
          <div class="s-product-card-donation-input gaming-donation-input">
            ${this.product?.donation?.can_donate ?
              `<label for="donation-amount-${this.product.id}">${this.donationAmount} <span>*</span></label>
              <input
                type="text"
                onInput="${e => {
                  salla.helpers.inputDigitsOnly(e.target);
                  this.addBtn.donatingAmount = (e.target).value;
                }}"
                id="donation-amount-${this.product.id}"
                name="donating_amount"
                class="s-form-control gaming-form-control"
                placeholder="${this.donationAmount}" />`
              : ``}
          </div>`
            : ''}
          <div class="s-product-card-content-sub ${this.isSpecial ? 's-product-card-content-extra-padding' : ''} gaming-product-sub">
            ${this.product?.donation?.can_donate ? '' : this.getProductPrice()}
            ${this.product?.rating?.stars ?
              `<div class="s-product-card-rating gaming-product-rating">
                <i class="sicon-star2 before:text-orange-300"></i>
                <span>${this.product.rating.stars}</span>
              </div>`
               : ``}
          </div>

          ${this.isSpecial && this.product.discount_ends
            ? `<salla-count-down date="${this.formatDate(this.product.discount_ends)}" end-of-day=${true} boxed=${true}
              labeled=${true} class="gaming-countdown" />`
            : ``}


          ${!this.hideAddBtn ?
            `<div class="s-product-card-content-footer gaming-product-footer gap-2">
              <salla-add-product-button fill="outline" width="wide"
                product-id="${this.product.id}"
                product-status="${this.product.status}"
                product-type="${this.product.type}"
                class="gaming-add-btn">
                ${this.product.status == 'sale' ? 
                    `<i class="text-base sicon-${ this.product.type == 'booking' ? 'calendar-time' : 'shopping-bag'}"></i>` : ``
                  }
                <span>${this.product.add_to_cart_label ? this.product.add_to_cart_label : this.getAddButtonLabel() }</span>
              </salla-add-product-button>

              ${this.horizontal || this.fullImage ?
                `<salla-button 
                  shape="icon" 
                  fill="outline" 
                  color="light" 
                  id="card-wishlist-btn-${this.product.id}-horizontal"
                  aria-label="Add or remove to wishlist"
                  class="s-product-card-wishlist-btn gaming-wishlist-btn animated ${this.isInWishlist ? 's-product-card-wishlist-added pulse-anime' : 'not-added un-favorited'}"
                  onclick="salla.wishlist.toggle(${this.product.id})"
                  data-id="${this.product.id}">
                  <i class="sicon-heart"></i> 
                </salla-button>`
                : ``}
            </div>`
            : ``}
            
          <div class="gaming-product-glow"></div>
        </div>
      `;

      // Add custom gaming styling
      this.addGamingStyle();

      this.querySelectorAll('[name="donating_amount"]').forEach((element)=>{
        element.addEventListener('input', (e) => {
          e.target
            .closest(".s-product-card-content")
            .querySelector("salla-add-product-button")
            .setAttribute("donating-amount", e.target.value); 
        });
      })

      document.lazyLoadInstance?.update(this.querySelectorAll('.lazy'));

      if (this.product?.quantity && this.isSpecial) {
        this.initCircleBar();
      }
    }

    // Add gaming style method
    addGamingStyle() {
      if (!document.getElementById('gaming-product-style')) {
        const style = document.createElement('style');
        style.id = 'gaming-product-style';
        style.textContent = `
          /* Gaming Product Card Styles */
          .gaming-product-card {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: rgba(10, 15, 20, 0.7);
            border: 1px solid rgba(29, 233, 182, 0.3);
          }
          
          .gaming-product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2), 0 0 15px rgba(29, 233, 182, 0.3);
          }
          
          .gaming-product-card:hover .gaming-product-glow {
            opacity: 1;
          }
          
          .gaming-product-card:hover .gaming-product-glow-border {
            opacity: 1;
          }
          
          .gaming-product-image {
            position: relative;
            overflow: hidden;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
          }
          
          .gaming-product-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(10, 15, 20, 0.6));
            z-index: 1;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
          
          .gaming-product-card:hover .gaming-product-image::after {
            opacity: 1;
          }
          
          .gaming-product-image-full {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
          }
          
          .gaming-product-glow-border {
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(29, 233, 182, 0.8), transparent);
            z-index: 5;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
          
          .gaming-product-glow {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 70px;
            background: radial-gradient(ellipse at center, rgba(29, 233, 182, 0.15) 0%, transparent 70%);
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
          }
          
          .gaming-product-content {
            position: relative;
            background: rgba(10, 15, 20, 0.5);
            padding: 1rem;
            z-index: 2;
          }
          
          .gaming-product-overlay {
            background: linear-gradient(to bottom, transparent, rgba(10, 15, 20, 0.7));
          }
          
          .gaming-product-title a {
            color: #fff !important;
            text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            transition: color 0.3s ease, text-shadow 0.3s ease;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .gaming-product-title a:hover {
            color: #1DE9B6 !important;
            text-shadow: 0 0 8px rgba(29, 233, 182, 0.5);
          }
          
          .gaming-product-subtitle {
            color: #ccc;
          }
          
          .gaming-product-sub h4 {
            color: #1DE9B6 !important;
            font-weight: 700;
            text-shadow: 0 0 8px rgba(29, 233, 182, 0.3);
          }
          
          .gaming-product-sub span {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: line-through;
          }
          
          .gaming-product-footer {
            margin-top: 1rem;
          }
          
          .gaming-wishlist-btn {
            background: rgba(10, 15, 20, 0.7) !important;
            border: 1px solid rgba(29, 233, 182, 0.3) !important;
            color: rgba(29, 233, 182, 0.8) !important;
            transition: all 0.3s ease !important;
            z-index: 5;
          }
          
          .gaming-wishlist-btn:hover {
            background: rgba(29, 233, 182, 0.15) !important;
            border-color: rgba(29, 233, 182, 0.5) !important;
            box-shadow: 0 0 10px rgba(29, 233, 182, 0.3) !important;
          }
          
          .gaming-wishlist-btn i {
            transition: all 0.3s ease;
          }
          
          .gaming-wishlist-btn:hover i {
            transform: scale(1.1);
          }
          
          .s-product-card-wishlist-added {
            background: rgba(29, 233, 182, 0.15) !important;
            border-color: rgba(29, 233, 182, 0.5) !important;
          }
          
          .s-product-card-wishlist-added i {
            color: #1DE9B6 !important;
          }
          
          .gaming-form-control {
            background: rgba(10, 15, 20, 0.5) !important;
            border: 1px solid rgba(29, 233, 182, 0.3) !important;
            color: #fff !important;
          }
          
          .gaming-form-control:focus {
            border-color: rgba(29, 233, 182, 0.7) !important;
            box-shadow: 0 0 0 3px rgba(29, 233, 182, 0.2) !important;
          }
          
          .gaming-product-card .s-product-card-promotion-title {
            background: linear-gradient(135deg, #1DE9B6, #0a1620) !important;
            color: #fff !important;
            font-weight: 600;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
            box-shadow: 0 0 10px rgba(29, 233, 182, 0.3);
          }
          
          .gaming-product-card .s-product-card-out-badge {
            background: rgba(255, 75, 75, 0.9) !important;
            color: #fff !important;
            font-weight: 600;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
          }
          
          .gaming-product-card .s-product-card-quantity {
            background: rgba(10, 15, 20, 0.7) !important;
            color: #1DE9B6 !important;
            border: 1px solid rgba(29, 233, 182, 0.3);
            font-weight: 600;
          }
          
          .gaming-product-pie {
            border: 1px solid rgba(29, 233, 182, 0.3);
            background: rgba(10, 15, 20, 0.5);
          }
          
          .gaming-product-pie span {
            color: #fff;
          }
          
          .gaming-product-pie b {
            color: #1DE9B6;
          }
          
          .gaming-product-card .s-product-card-content-pie-svg-base {
            stroke: rgba(29, 233, 182, 0.1);
          }
          
          .gaming-product-card .s-product-card-content-pie-svg-bar {
            stroke: rgba(29, 233, 182, 0.8);
            filter: drop-shadow(0 0 3px rgba(29, 233, 182, 0.5));
          }
          
          .gaming-product-rating {
            display: flex;
            align-items: center;
            gap: 3px;
          }
          
          .gaming-product-rating i {
            color: #FFD700 !important;
            filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));
          }
          
          .gaming-product-rating span {
            color: #FFD700 !important;
            font-weight: 600;
          }
          
          /* Minimal card tweaks */
          .s-product-card-minimal.gaming-product-card {
            background: transparent;
            border: none;
          }
          
          .s-product-card-minimal.gaming-product-card .gaming-product-content {
            background: transparent;
          }
          
          .s-product-card-minimal.gaming-product-card:hover {
            transform: translateY(-3px);
            box-shadow: none;
          }
          
          .s-product-card-minimal .gaming-product-image::after {
            background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(10, 15, 20, 0.3));
          }
          
          /* Animation for the glow effect */
          @keyframes glow-pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
          }
        `;
        document.head.appendChild(style);
      }
    }
}

customElements.define('custom-salla-product-card', ProductCard);