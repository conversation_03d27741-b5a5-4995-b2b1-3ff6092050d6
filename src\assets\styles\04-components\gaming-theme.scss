/*
  Gaming Theme Components
*/

/* Second Gallery Component */
.gaming-gallery-section {
    position: relative;
    padding: 3rem 0;
    margin: 0 auto;
    overflow: hidden;
}

.gaming-gallery-container {
    position: relative;
    z-index: 2;
}

.gaming-gallery-title-wrapper {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.gaming-gallery-title {
    color: #fff;
    font-size: 2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    display: inline-block;
    margin: 0 auto;
    position: relative;
    padding: 0 1.5rem;
    text-shadow: 0 0 10px rgba(29, 233, 182, 0.5);
}

.gaming-title-underline {
    position: relative;
    height: 3px;
    width: 100px;
    margin: 1rem auto 0;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(29, 233, 182, 0.8), 
        transparent);
    box-shadow: 0 0 10px rgba(29, 233, 182, 0.6);
}

.gaming-gallery-slider {
    position: relative;
    overflow: hidden;
    padding: 1rem 0;
}

.gaming-gallery-track {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    transition: transform 0.5s ease-in-out;
}

.with-slider .gaming-gallery-track {
    flex-wrap: nowrap;
    justify-content: flex-start;
    transform: translateX(0);
}

.gaming-gallery-item {
    flex: 0 0 calc(33.3333% - 2rem);
    max-width: calc(33.3333% - 2rem);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    transition: transform 0.3s ease;
}

.gaming-gallery-item:hover {
    transform: translateY(-10px);
}

.gaming-gallery-image-wrapper {
    position: relative;
    width: 150px;
    height: 150px;
    margin-bottom: 1.5rem;
}

.gaming-gallery-image-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(29, 233, 182, 0.3) 0%, rgba(29, 233, 182, 0) 70%);
    z-index: 1;
    opacity: 0.5;
    transition: opacity 0.3s ease, width 0.3s ease, height 0.3s ease;
}

.gaming-gallery-item:hover .gaming-gallery-image-glow {
    opacity: 1;
    width: 160px;
    height: 160px;
}

.gaming-gallery-image-container {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid rgba(29, 233, 182, 0.5);
    overflow: hidden;
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.3);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.gaming-gallery-item:hover .gaming-gallery-image-container {
    box-shadow: 0 0 20px rgba(29, 233, 182, 0.5);
    transform: scale(1.05);
}

.gaming-gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gaming-gallery-item:hover .gaming-gallery-image {
    transform: scale(1.1);
}

.gaming-gallery-item-title {
    color: #fff;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    margin: 0;
    transition: color 0.3s ease, text-shadow 0.3s ease;
}

.gaming-gallery-item:hover .gaming-gallery-item-title {
    color: #1DE9B6;
    text-shadow: 0 0 8px rgba(29, 233, 182, 0.5);
}

.gaming-gallery-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2rem;
    gap: 1rem;
}

.gaming-gallery-prev-btn,
.gaming-gallery-next-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(29, 233, 182, 0.1);
    border: 1px solid rgba(29, 233, 182, 0.3);
    color: #1DE9B6;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gaming-gallery-prev-btn:hover,
.gaming-gallery-next-btn:hover {
    background: rgba(29, 233, 182, 0.2);
    box-shadow: 0 0 10px rgba(29, 233, 182, 0.4);
}

.gaming-gallery-dots {
    display: flex;
    gap: 0.5rem;
}

.gaming-gallery-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gaming-gallery-dot.active {
    background: #1DE9B6;
    box-shadow: 0 0 10px rgba(29, 233, 182, 0.5);
}

.gaming-gallery-dot:hover {
    background: rgba(29, 233, 182, 0.5);
}

.gaming-gallery-circuit-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 304 304' width='304' height='304'%3E%3Cpath fill='%231de9b6' fill-opacity='0.05' d='M44.1 224a5 5 0 1 1 0 2H0v-2h44.1zm160 48a5 5 0 1 1 0 2H82v-2h122.1zm57.8-46a5 5 0 1 1 0-2H304v2h-42.1zm0 16a5 5 0 1 1 0-2H304v2h-42.1zm6.2-114a5 5 0 1 1 0 2h-86.2a5 5 0 1 1 0-2h86.2zm-256-48a5 5 0 1 1 0 2H0v-2h12.1zm185.8 34a5 5 0 1 1 0-2h86.2a5 5 0 1 1 0 2h-86.2zM258 12.1a5 5 0 1 1-2 0V0h2v12.1zm-64 208a5 5 0 1 1-2 0v-54.2a5 5 0 1 1 2 0v54.2zm48-198.2V80h62v2h-64V21.9a5 5 0 1 1 2 0zm16 16V64h46v2h-48V37.9a5 5 0 1 1 2 0zm-128 96V208h16v12.1a5 5 0 1 1-2 0V210h-16v-76.1a5 5 0 1 1 2 0z'%3E%3C/path%3E%3C/svg%3E");
    opacity: 0.15;
    z-index: 1;
}

@keyframes galleryParticleFloat {
    0% { transform: translateY(0) translateX(0); }
    25% { transform: translateY(-20px) translateX(10px); }
    50% { transform: translateY(0) translateX(20px); }
    75% { transform: translateY(20px) translateX(10px); }
    100% { transform: translateY(0) translateX(0); }
}

@media (max-width: 991px) {
    .gaming-gallery-item {
        flex: 0 0 calc(50% - 2rem);
        max-width: calc(50% - 2rem);
    }
    
    .with-slider .gaming-gallery-item {
        flex: 0 0 calc(50% - 2rem);
        max-width: calc(50% - 2rem);
    }
}

@media (max-width: 576px) {
    .gaming-gallery-title {
        font-size: 1.5rem;
    }
    
    .gaming-gallery-item {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .with-slider .gaming-gallery-item {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .gaming-gallery-image-wrapper {
        width: 120px;
        height: 120px;
    }
    
    .gaming-gallery-item:hover .gaming-gallery-image-glow {
        width: 130px;
        height: 130px;
    }
}

/* Gaming Search Component */
.gaming-search {
    position: relative;
    width: 100%;
    
    .gaming-search-input-container {
        position: relative;
        width: 100%;
    }
    
    .gaming-search-input {
        width: 100%;
        border-radius: 9999px;
        padding: 0.75rem 2.5rem 0.75rem 1.5rem;
        background-color: rgba(30, 30, 50, 0.5);
        border: 1px solid rgba(29, 233, 182, 0.3);
        color: #fff;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        box-shadow: 0 0 15px rgba(29, 233, 182, 0.1) inset;
        
        &:focus {
            outline: none;
            border-color: rgba(29, 233, 182, 0.7);
            box-shadow: 0 0 15px rgba(29, 233, 182, 0.2) inset,
                        0 0 10px rgba(29, 233, 182, 0.2);
        }
        
        &::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
    }
    
    .gaming-search-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        color: #1DE9B6;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        
        .rtl & {
            left: 1rem;
        }
        
        .ltr & {
            right: 1rem;
        }
    }
    
    &:hover .gaming-search-icon {
        text-shadow: 0 0 10px rgba(29, 233, 182, 0.7);
    }
    
    .gaming-search-results {
        position: absolute;
        z-index: 99999;
        margin-top: 0.5rem;
        width: 100%;
        background-color: rgba(10, 10, 24, 0.95);
        border-radius: 0.75rem;
        border: 1px solid rgba(29, 233, 182, 0.3);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5),
                    0 0 15px rgba(29, 233, 182, 0.2);
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease, opacity 0.3s ease;
        opacity: 0;
        backdrop-filter: blur(10px);
        
        &.gaming-search-results-open {
            max-height: 500px;
            opacity: 1;
            overflow-y: auto;
        }
    }
    
    .gaming-search-section-title {
        font-size: 0.9rem;
        font-weight: 600;
        padding: 0.75rem 1rem;
        background-color: rgba(29, 233, 182, 0.1);
        color: #1DE9B6;
        border-bottom: 1px solid rgba(29, 233, 182, 0.2);
        letter-spacing: 1px;
        text-transform: uppercase;
    }
    
    .gaming-search-result-item {
        display: block;
        padding: 0.75rem 1rem;
        color: #fff;
        transition: all 0.2s ease;
        position: relative;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        
        &:hover, &:focus {
            background-color: rgba(29, 233, 182, 0.1);
            transform: translateX(5px);
            outline: none;
        }
        
        &:focus {
            box-shadow: 0 0 0 2px rgba(29, 233, 182, 0.5) inset;
        }
        
        &.high-relevance {
            background-color: rgba(29, 233, 182, 0.05);
            border-left: 3px solid rgba(29, 233, 182, 0.7);
            
            &:hover {
                background-color: rgba(29, 233, 182, 0.15);
            }
        }
    }
    
    .gaming-search-result-wrapper {
        display: flex;
        align-items: center;
    }
    
    .gaming-search-result-image {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        object-fit: cover;
        margin-right: 0.75rem;
        border: 1px solid rgba(29, 233, 182, 0.2);
        background-color: rgba(0, 0, 0, 0.3);
        padding: 2px;
        
        .rtl & {
            margin-right: 0;
            margin-left: 0.75rem;
        }
    }
    
    .gaming-search-result-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        margin-right: 0.75rem;
        background-color: rgba(29, 233, 182, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #1DE9B6;
        font-size: 1.2rem;
        
        .rtl & {
            margin-right: 0;
            margin-left: 0.75rem;
        }
    }
    
    .gaming-search-result-content {
        flex: 1;
    }
    
    .gaming-search-result-name {
        font-weight: 500;
        margin-bottom: 0.25rem;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: color 0.3s;
        
        .high-relevance & {
            color: #1DE9B6;
            text-shadow: 0 0 5px rgba(29, 233, 182, 0.3);
        }
    }
    
    .gaming-search-result-price {
        font-size: 0.8rem;
    }
    
    .gaming-search-result-current-price {
        color: #1DE9B6;
        font-weight: 600;
    }
    
    .gaming-search-result-old-price {
        text-decoration: line-through;
        color: rgba(255, 255, 255, 0.5);
        margin-left: 0.5rem;
    }
    
    .gaming-search-loading,
    .gaming-search-no-results {
        padding: 1.5rem;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
        display: none;
    }
    
    .gaming-search-loading-visible {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
    }
    
    .gaming-search-no-results-visible {
        display: block;
    }
    
    .gaming-search-loading-spinner {
        display: inline-block;
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 50%;
        border: 2px solid rgba(29, 233, 182, 0.1);
        border-top-color: #1DE9B6;
        animation: searchSpin 1s linear infinite;
    }
}

.gaming-search-container {
    position: relative;
    
    .gaming-search-glow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        height: 40px;
        width: 100%;
        border-radius: 9999px;
        background: radial-gradient(
            ellipse at center,
            rgba(29, 233, 182, 0.15) 0%,
            rgba(29, 233, 182, 0) 70%
        );
        z-index: -1;
        filter: blur(8px);
        opacity: 0.5;
        transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    &:hover .gaming-search-glow {
        opacity: 0.8;
        transform: translateY(-50%) scale(1.05);
    }
}

@keyframes searchSpin {
    to {
        transform: rotate(360deg);
    }
}

/* Media Queries */
@media (max-width: 767px) {
    .gaming-search {
        .gaming-search-input {
            padding: 0.6rem 2.5rem 0.6rem 1rem;
            font-size: 0.85rem;
        }
        
        .gaming-search-result-image,
        .gaming-search-result-icon {
            width: 35px;
            height: 35px;
        }
        
        .gaming-search-section-title {
            font-size: 0.8rem;
            padding: 0.5rem 1rem;
        }
        
        .gaming-search-result-name {
            font-size: 0.9rem;
        }
    }
} 