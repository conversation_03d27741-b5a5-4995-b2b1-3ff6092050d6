<footer class="store-footer gaming-footer">
	<div aria-label="footer" class="store-footer__inner">
		<div class="container grid grid-col-1 lg:grid-cols-6 gap-8 lg:gap-6">
			<div class="lg:col-span-2 rtl:lg:pl-20 ltr:lg:pr-20">
				<a href="{{ link('/') }}" class="flex items-center m-0 gaming-logo">
					<h3>{{store.name}}</h3>
				</a>
				{% if store.description %}
					<p class="max-w-sm leading-6 mb-6 footer-description">
						{{ store.description|raw }}
					</p>
				{% endif %}

				<div class="hidden lg:block">
					<salla-social class="mb-6 gaming-social"></salla-social>
				</div>

          {# vat #}
          {% if store.settings.tax.number %}
            <div class="flex rtl:space-x-reverse space-x-2 items-end gaming-tax-info">
              {% if store.settings.tax.certificate %}
                <a class="load-img-onclick gaming-tax-img" data-modal-id="modal-value-tax" href="#/" alt="{{ store.settings.tax.number }}">
                  <img width="100%" height="100%" src="{{ 'images/s-empty.png' | cdn }}" data-src="{{'images/tax.png' | cdn(70,70)}}" class="lazy w-10 rounded-sm hover:opacity-80 transition-opacity" alt="value added tax">
                </a>
              {% endif %}
              <div>
                <p class="text-sm text-text-grey mb-1">{{ trans('common.elements.tax_number') }}</p>
                <b class="text-sm">{{ store.settings.tax.number }}</b>
              </div>
            </div>

            {% if store.settings.tax.certificate %}
              <salla-modal sub-title-first sub-title="{{ trans('common.elements.tax_number') }}" modal-title="{{ store.settings.tax.number }}" id="modal-value-tax" class="gaming-modal">
                <div class="flex justify-center max-w-full">
                  <img class="w-full" loading="lazy" 
                  src="{{ 'images/s-empty.png' | cdn }}" 
                  data-src="{{ store.settings.tax.certificate }}" 
                  alt="{{ store.settings.tax.number }}"/>
                </div>
              </salla-modal>
            {% endif %}
          {% endif %}
        </div>

			<div class="gaming-footer-links">
				<h3 class="gaming-title">{{ trans('blocks.footer.pages_links') }}</h3>
				<salla-menu source="footer"></salla-menu>
			</div>

			<div class="gaming-footer-contacts">
				<salla-contacts></salla-contacts>

				<div class="lg:hidden contact-social">
					<salla-social class="gaming-social"></salla-social>
				</div>
			</div>

			<salla-apps-icons class="lg:col-span-2 rtl:lg:pr-20 ltr:lg:pl-20 gaming-apps"></salla-apps-icons>

		</div>
	</div>

	<div class="md:flex items-center justify-between py-4 container text-center gaming-footer-bottom">
		<span class="text-sm copyright-text">{% hook copyright %}</span>
		<salla-payments class="gaming-payments"></salla-payments>
	</div>
</div>

<span class="hidden bottom-2 rtl:pl-24 ltr:pr-24 md:grid-cols-3 opacity-40"></span>

<style>
/* Gaming theme footer styling */
.gaming-footer {
    background: linear-gradient(to bottom, rgba(62, 62, 141, 0), rgba(5,10,20,0.98));
    border-top: 1px solid rgba(29, 233, 182, 0.3);
    box-shadow: 0 -5px 20px rgba(29, 233, 182, 0.15);
    position: relative;
    overflow: hidden;
    color: #f5f5f5;
    padding-top: 3rem;
}

.gaming-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(29, 233, 182, 0.7), 
        transparent);
    box-shadow: 0 0 20px rgba(29, 233, 182, 0.8);
}

/* Circuit board pattern for gaming effect */
.gaming-footer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 304 304' width='304' height='304'%3E%3Cpath fill='%231de9b6' fill-opacity='0.05' d='M44.1 224a5 5 0 1 1 0 2H0v-2h44.1zm160 48a5 5 0 1 1 0 2H82v-2h122.1zm57.8-46a5 5 0 1 1 0-2H304v2h-42.1zm0 16a5 5 0 1 1 0-2H304v2h-42.1zm6.2-114a5 5 0 1 1 0 2h-86.2a5 5 0 1 1 0-2h86.2zm-256-48a5 5 0 1 1 0 2H0v-2h12.1zm185.8 34a5 5 0 1 1 0-2h86.2a5 5 0 1 1 0 2h-86.2zM258 12.1a5 5 0 1 1-2 0V0h2v12.1zm-64 208a5 5 0 1 1-2 0v-54.2a5 5 0 1 1 2 0v54.2zm48-198.2V80h62v2h-64V21.9a5 5 0 1 1 2 0zm16 16V64h46v2h-48V37.9a5 5 0 1 1 2 0zm-128 96V208h16v12.1a5 5 0 1 1-2 0V210h-16v-76.1a5 5 0 1 1 2 0zm-5.9-21.9a5 5 0 1 1 0 2H114v48H85.9a5 5 0 1 1 0-2H112v-48h12.1zm-6.2 130a5 5 0 1 1 0-2H176v-74.1a5 5 0 1 1 2 0V242h-60.1zm-16-64a5 5 0 1 1 0-2H114v48h10.1a5 5 0 1 1 0 2H112v-48h-10.1zM66 284.1a5 5 0 1 1-2 0V274H50v30h-2v-32h18v12.1zM236.1 176a5 5 0 1 1 0 2H226v94h48v32h-2v-30h-48v-98h12.1zm25.8-30a5 5 0 1 1 0-2H274v44.1a5 5 0 1 1-2 0V146h-10.1zm-64 96a5 5 0 1 1 0-2H208v-80h16v-14h-42.1a5 5 0 1 1 0-2H226v18h-16v80h-12.1zm86.2-210a5 5 0 1 1 0 2H272V0h2v32h10.1zM98 101.9V146H53.9a5 5 0 1 1 0-2H96v-42.1a5 5 0 1 1 2 0zM53.9 34a5 5 0 1 1 0-2H80V0h2v34H53.9zm60.1 3.9V66H82v64H69.9a5 5 0 1 1 0-2H80V64h32V37.9a5 5 0 1 1 2 0zM101.9 82a5 5 0 1 1 0-2H128V37.9a5 5 0 1 1 2 0V82h-28.1zm16-64a5 5 0 1 1 0-2H146v44.1a5 5 0 1 1-2 0V18h-26.1zm102.2 270a5 5 0 1 1 0 2H98v14h-2v-16h124.1zM242 149.9V160h16v34h-16v62h48v48h-2v-46h-48v-66h16v-30h-16v-12.1a5 5 0 1 1 2 0zM53.9 18a5 5 0 1 1 0-2H64V2H48V0h18v18H53.9zm112 32a5 5 0 1 1 0-2H192V0h50v2h-48v48h-28.1zm-48-48a5 5 0 0 1-9.8-2h2.07a3 3 0 1 0 5.66 0H178v34h-18V21.9a5 5 0 1 1 2 0V32h14V2h-58.1zm0 96a5 5 0 1 1 0-2H137l32-32h39V21.9a5 5 0 1 1 2 0V66h-40.17l-32 32H117.9zm28.1 90.1a5 5 0 1 1-2 0v-76.51L175.59 80H224V21.9a5 5 0 1 1 2 0V82h-49.59L146 112.41v75.69zm16 32a5 5 0 1 1-2 0v-99.51L184.59 96H300.1a5 5 0 0 1 3.9-3.9v2.07a3 3 0 0 0 0 5.66v2.07a5 5 0 0 1-3.9-3.9H185.41L162 121.41v98.69zm-144-64a5 5 0 1 1-2 0v-3.51l48-48V48h32V0h2v50H66v55.41l-48 48v2.69zM50 53.9v43.51l-48 48V208h26.1a5 5 0 1 1 0 2H0v-65.41l48-48V53.9a5 5 0 1 1 2 0zm-16 16V89.41l-34 34v-2.82l32-32V69.9a5 5 0 1 1 2 0zM12.1 32a5 5 0 1 1 0 2H9.41L0 43.41V40.6L8.59 32h3.51zm265.8 18a5 5 0 1 1 0-2h18.69l7.41-7.41v2.82L297.41 50H277.9zm-16 160a5 5 0 1 1 0-2H288v-71.41l16-16v2.82l-14 14V210h-28.1zm-208 32a5 5 0 1 1 0-2H64v-22.59L40.59 194H21.9a5 5 0 1 1 0-2H41.41L66 216.59V242H53.9zm150.2 14a5 5 0 1 1 0 2H96v-56.6L56.6 162H37.9a5 5 0 1 1 0-2h19.5L98 200.6V256h106.1zm-150.2 2a5 5 0 1 1 0-2H80v-46.59L48.59 178H21.9a5 5 0 1 1 0-2H49.41L82 208.59V258H53.9zM34 39.8v1.61L9.41 66H0v-2h8.59L32 40.59V0h2v39.8zM2 300.1a5 5 0 0 1 3.9 3.9H3.83A3 3 0 0 0 0 302.17V256h18v48h-2v-46H2v42.1zM34 241v63h-2v-62H0v-2h34v1zM17 18H0v-2h16V0h2v18h-1zm273-2h14v2h-16V0h2v16zm-32 273v15h-2v-14h-14v14h-2v-16h18v1zM0 92.1A5.02 5.02 0 0 1 6 97a5 5 0 0 1-6 4.9v-2.07a3 3 0 1 0 0-5.66V92.1zM80 272h2v32h-2v-32zm37.9 32h-2.07a3 3 0 0 0-5.66 0h-2.07a5 5 0 0 1 9.8 0zM5.9 0A5.02 5.02 0 0 1 0 5.9V3.83A3 3 0 0 0 3.83 0H5.9zm294.2 0h2.07A3 3 0 0 0 304 3.83V5.9a5 5 0 0 1-3.9-5.9zm3.9 300.1v2.07a3 3 0 0 0-1.83 1.83h-2.07a5 5 0 0 1 3.9-3.9zM97 100a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-48 32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm32 48a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm32-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0-32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm32 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16-64a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 96a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16-144a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16-32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-96 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16-32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm96 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16-64a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-32 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zM49 36a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-32 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm32 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zM33 68a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16-48a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 240a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16-64a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16-32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm80-176a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm32 48a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0-32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm112 176a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-16 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zM17 180a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 16a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0-32a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6zM17 84a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm32 64a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm16-16a3 3 0 1 0 0-6 3 3 0 0 0 0 6z'%3E%3C/path%3E%3C/svg%3E");
    opacity: 0.15;
    z-index: 0;
}

.gaming-footer h3 {
    color: #1DE9B6;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(29, 233, 182, 0.5);
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
}

.gaming-logo h3 {
    font-size: 1.75rem;
    letter-spacing: 1px;
    transition: text-shadow 0.3s ease;
}

.gaming-logo:hover h3 {
    text-shadow: 0 0 15px rgba(29, 233, 182, 0.8);
}

.footer-description {
    color: #ccc;
    line-height: 1.6;
    border-left: 2px solid rgba(29, 233, 182, 0.5);
    padding-left: 1rem;
    position: relative;
    z-index: 1;
}

.gaming-footer-links a, 
.gaming-footer-contacts a {
    color: #eee;
    transition: all 0.3s ease;
    display: inline-block;
    padding: 0.3rem 0;
    position: relative;
    z-index: 1;
}

.gaming-footer-links a:hover, 
.gaming-footer-contacts a:hover {
    color: #1DE9B6;
    text-shadow: 0 0 8px rgba(29, 233, 182, 0.4);
    transform: translateX(5px);
}

/* Enhanced social icons */
.gaming-social {
    position: relative;
    z-index: 1;
}

.gaming-social a {
    background: rgba(10, 15, 20, 0.5) !important;
    border: 1px solid rgba(29, 233, 182, 0.3) !important;
    transition: all 0.3s ease !important;
    position: relative;
    overflow: hidden;
    margin-right: 8px !important;
}

.gaming-social a:hover {
    background: rgba(29, 233, 182, 0.15) !important;
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.5) !important;
    transform: translateY(-3px) !important;
}

.gaming-social a::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        transparent, 
        transparent,
        transparent, 
        rgba(29, 233, 182, 0.3)
    );
    transform: rotate(45deg);
    animation: shine-sweep 3s infinite linear;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gaming-social a:hover::before {
    opacity: 1;
}

@keyframes shine-sweep {
    0% {
        top: -50%;
        left: -50%;
    }
    100% {
        top: 150%;
        left: 150%;
    }
}

.gaming-social svg {
    fill: rgba(29, 233, 182, 0.8) !important;
    filter: drop-shadow(0 0 3px rgba(29, 233, 182, 0.5));
}

.gaming-tax-info {
    background: rgba(10, 15, 20, 0.5);
    padding: 0.75rem;
    border-radius: 5px;
    border: 1px solid rgba(29, 233, 182, 0.2);
    position: relative;
    z-index: 1;
}

.gaming-tax-img img {
    border: 1px solid rgba(29, 233, 182, 0.3);
    transition: all 0.3s ease;
}

.gaming-tax-img:hover img {
    box-shadow: 0 0 10px rgba(29, 233, 182, 0.5);
}

.gaming-footer-bottom {
    border-top: 1px solid rgba(29, 233, 182, 0.2);
    position: relative;
    background: rgba(5, 10, 15, 0.7);
}

.gaming-payments {
    opacity: 0.9;
    transition: opacity 0.3s ease;
    position: relative;
    z-index: 1;
}

.gaming-payments:hover {
    opacity: 1;
}

.gaming-apps {
    position: relative;
    z-index: 1;
}

.gaming-apps img {
    filter: grayscale(70%) brightness(1.2);
    transition: all 0.3s ease;
    border-radius: 5px;
    padding: 3px;
    background: rgba(10, 15, 20, 0.3);
}

.gaming-apps img:hover {
    filter: grayscale(0%) brightness(1.3);
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.3);
    background: rgba(29, 233, 182, 0.05);
}

/* Moving particles effect */
.gaming-footer .particle {
    position: absolute;
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: rgba(29, 233, 182, 0.6);
    box-shadow: 0 0 10px rgba(29, 233, 182, 0.8);
    pointer-events: none;
    z-index: 0;
}

/* Add pulsing effect on some elements */
@keyframes subtle-pulse {
    0% { opacity: 0.8; box-shadow: 0 0 5px rgba(29, 233, 182, 0.5); }
    50% { opacity: 1; box-shadow: 0 0 15px rgba(29, 233, 182, 0.8); }
    100% { opacity: 0.8; box-shadow: 0 0 5px rgba(29, 233, 182, 0.5); }
}

.gaming-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #1DE9B6;
    box-shadow: 0 0 10px rgba(29, 233, 182, 0.7);
    animation: subtle-pulse 3s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .gaming-footer {
        padding-top: 2rem;
    }
    
    .gaming-footer h3 {
        font-size: 1.2rem;
    }
    
    .footer-description {
        font-size: 0.9rem;
    }
    
    .gaming-social a {
        margin-right: 5px !important;
    }
}
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Create animated particles in the footer
        const footer = document.querySelector('.gaming-footer');
        if (footer) {
            // Create 15 particles
            for (let i = 0; i < 15; i++) {
                createParticle(footer);
            }
        }
        
        // Log initialization
        console.log('Gaming footer initialized with enhanced background and social icons');
    });
    
    function createParticle(parent) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // Random position
        const posX = Math.random() * parent.offsetWidth;
        const posY = Math.random() * parent.offsetHeight;
        
        // Random size (1-3px)
        const size = 1 + Math.random() * 2;
        
        // Random opacity
        const opacity = 0.3 + Math.random() * 0.5;
        
        // Set styles
        particle.style.left = posX + 'px';
        particle.style.top = posY + 'px';
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.opacity = opacity;
        
        // Add to parent
        parent.appendChild(particle);
        
        // Animate particle
        animateParticle(particle, parent);
    }
    
    function animateParticle(particle, parent) {
        // Random duration (20-40s)
        const duration = 20000 + Math.random() * 20000;
        
        // Random new position
        const newPosX = Math.random() * parent.offsetWidth;
        const newPosY = Math.random() * parent.offsetHeight;
        
        // Animation
        particle.animate(
            [
                { opacity: particle.style.opacity },
                { opacity: 0.1 + Math.random() * 0.5 },
                { opacity: particle.style.opacity }
            ],
            {
                duration: duration,
                iterations: Infinity
            }
        );
        
        // Move particle
        particle.animate(
            [
                { transform: 'translate(0, 0)' },
                { transform: `translate(${newPosX - parseFloat(particle.style.left)}px, ${newPosY - parseFloat(particle.style.top)}px)` }
            ],
            {
                duration: duration,
                iterations: Infinity,
                direction: 'alternate',
                easing: 'ease-in-out'
            }
        );
    }
</script>
