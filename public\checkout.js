/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _assertThisInitialized)\n/* harmony export */ });\nfunction _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _classCallCheck)\n/* harmony export */ });\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _createClass)\n/* harmony export */ });\n/* harmony import */ var _toPropertyKey_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./toPropertyKey.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js\");\n\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, (0,_toPropertyKey_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _getPrototypeOf)\n/* harmony export */ });\nfunction _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _inherits)\n/* harmony export */ });\n/* harmony import */ var _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./setPrototypeOf.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js\");\n\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && (0,_setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(t, e);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _possibleConstructorReturn)\n/* harmony export */ });\n/* harmony import */ var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./typeof.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _assertThisInitialized_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assertThisInitialized.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n\n\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == (0,_typeof_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return (0,_assertThisInitialized_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _setPrototypeOf)\n/* harmony export */ });\nfunction _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPrimitive.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPrimitive.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ toPrimitive)\n/* harmony export */ });\n/* harmony import */ var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./typeof.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != (0,_typeof_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != (0,_typeof_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPrimitive.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ toPropertyKey)\n/* harmony export */ });\n/* harmony import */ var _typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./typeof.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _toPrimitive_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toPrimitive.js */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPrimitive.js\");\n\n\nfunction toPropertyKey(t) {\n  var i = (0,_toPrimitive_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t, \"string\");\n  return \"symbol\" == (0,_typeof_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(i) ? i : i + \"\";\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js?");

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _typeof)\n/* harmony export */ });\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\n\n//# sourceURL=webpack://theme-raed/./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/typeof.js?");

/***/ }),

/***/ "./src/assets/js/base-page.js":
/*!************************************!*\
  !*** ./src/assets/js/base-page.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n\n\nvar BasePage = /*#__PURE__*/function () {\n  function BasePage() {\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, BasePage);\n  }\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(BasePage, [{\n    key: \"onReady\",\n    value: function onReady() {\n      //\n    }\n  }, {\n    key: \"registerEvents\",\n    value: function registerEvents() {\n      //\n    }\n\n    /**\r\n     * To avoid loading unwanted classes, unless it's wanted page\r\n     * @param {null|string[]} allowedPages\r\n     * @return {*}\r\n     */\n  }, {\n    key: \"initiate\",\n    value: function initiate(allowedPages) {\n      if (allowedPages && !allowedPages.includes(salla.config.get('page.slug'))) {\n        return app.log(\"The Class For (\".concat(allowedPages.join(','), \") Skipped.\"));\n      }\n      this.onReady();\n      this.registerEvents();\n      app.log(\"The Class For (\".concat((allowedPages === null || allowedPages === void 0 ? void 0 : allowedPages.join(',')) || '*', \") Loaded\\uD83C\\uDF89\"));\n    }\n  }]);\n}();\n/**\r\n * Because we merged multi classes into one file, there is no need to initiate all of them\r\n */\nBasePage.initiateWhenReady = function () {\n  var _window$app,\n    _this = this;\n  var allowedPages = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n  if (((_window$app = window.app) === null || _window$app === void 0 ? void 0 : _window$app.status) === 'ready') {\n    new this().initiate(allowedPages);\n  } else {\n    document.addEventListener('theme::ready', function () {\n      return new _this().initiate(allowedPages);\n    });\n  }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BasePage);\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/base-page.js?");

/***/ }),

/***/ "./src/assets/js/cart.js":
/*!*******************************!*\
  !*** ./src/assets/js/cart.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _base_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./base-page */ \"./src/assets/js/base-page.js\");\n/* harmony import */ var _partials_validate_product_options__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./partials/validate-product-options */ \"./src/assets/js/partials/validate-product-options.js\");\n\n\n\n\n\nfunction _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\n\n\nvar Cart = /*#__PURE__*/function (_BasePage) {\n  function Cart() {\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, Cart);\n    return _callSuper(this, Cart, arguments);\n  }\n  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(Cart, _BasePage);\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Cart, [{\n    key: \"onReady\",\n    value: function onReady() {\n      var _this = this;\n      // keep update the dom base in the events\n      salla.event.cart.onUpdated(function (data) {\n        return _this.updateCartPageInfo(data);\n      });\n      app.watchElements({\n        couponCodeInput: '#coupon-input',\n        couponBtn: '#coupon-btn',\n        couponError: '#coupon-error',\n        subTotal: '#sub-total',\n        orderOptionsTotal: '#cart-options-total',\n        totalDiscount: '#total-discount',\n        taxAmount: '#tax-amount',\n        shippingCost: '#shipping-cost',\n        freeShipping: '#free-shipping',\n        freeShippingBar: '#free-shipping-bar',\n        freeShippingMsg: '#free-shipping-msg',\n        freeShipApplied: '#free-shipping-applied',\n        cartGifting: '#cart-gifting',\n        sallaGifting: '#salla-gifting'\n      });\n      this.initiateCoupon();\n      this.initSubmitCart();\n      (0,_partials_validate_product_options__WEBPACK_IMPORTED_MODULE_6__.validateProductOptions)();\n    }\n  }, {\n    key: \"initSubmitCart\",\n    value: function initSubmitCart() {\n      var submitBtn = document.querySelector('#cart-submit');\n      var cartForms = document.querySelectorAll('form[id^=\"item-\"]');\n      if (!submitBtn) {\n        return;\n      }\n      app.onClick(submitBtn, function (event) {\n        var isValid = true;\n        cartForms.forEach(function (form) {\n          isValid = isValid && form.reportValidity();\n          if (!isValid) {\n            event.preventDefault();\n            salla.notify.error(salla.lang.get('common.messages.required_fields'));\n            return;\n          }\n        });\n        if (isValid) {\n          /** @type HTMLSallaButtonElement */\n          var btn = event.currentTarget;\n          salla.config.get('user.type') == 'guest' ? salla.cart.submit() : btn.load().then(function () {\n            return salla.cart.submit();\n          });\n        }\n      });\n    }\n  }, {\n    key: \"updateCartOptions\",\n    value: function updateCartOptions(options) {\n      var _document$querySelect;\n      if (!options || !options.length) return;\n      var arrayTwoId = options.map(function (item) {\n        return item.id;\n      });\n      (_document$querySelect = document.querySelectorAll('.cart-options form')) === null || _document$querySelect === void 0 || _document$querySelect.forEach(function (form) {\n        if (!arrayTwoId.includes(parseInt(form.id.value))) {\n          form.remove();\n        }\n      });\n    }\n\n    /**\r\n     * @param {import(\"@salla.sa/twilight/types/api/cart\").CartSummary} cartData\r\n     */\n  }, {\n    key: \"updateCartPageInfo\",\n    value: function updateCartPageInfo(cartData) {\n      var _app$sallaGifting,\n        _app$sallaGifting2,\n        _cartData$items,\n        _this2 = this;\n      //if item deleted & there is no more items, just reload the page\n      if (!cartData.count) {\n        var _document$querySelect2;\n        // clear cart options from the dom before page reload\n        (_document$querySelect2 = document.querySelector('.cart-options')) === null || _document$querySelect2 === void 0 || _document$querySelect2.remove();\n        return window.location.reload();\n      }\n      // toggle physical gifting depned on giftable flag\n      app.toggleElementClassIf(app.cartGifting, 'active', 'hidden', function () {\n        return cartData.gift.enabled;\n      });\n      // Use toggleAttribute to handle the `physical-products` attribute\n      (_app$sallaGifting = app.sallaGifting) === null || _app$sallaGifting === void 0 || _app$sallaGifting.toggleAttribute('physical-products', cartData.gift.type === 'physical');\n      (_app$sallaGifting2 = app.sallaGifting) === null || _app$sallaGifting2 === void 0 || _app$sallaGifting2.toggleAttribute('digital-products', cartData.gift.type === 'digital');\n\n      // update the dom for cart options\n      this.updateCartOptions(cartData === null || cartData === void 0 ? void 0 : cartData.options);\n      // update each item data\n      (_cartData$items = cartData.items) === null || _cartData$items === void 0 || _cartData$items.forEach(function (item) {\n        return _this2.updateItemInfo(item);\n      });\n      app.subTotal.innerHTML = salla.money(cartData.sub_total);\n      if (app.taxAmount) app.taxAmount.innerHTML = salla.money(cartData.tax_amount);\n      if (app.orderOptionsTotal) app.orderOptionsTotal.innerHTML = salla.money(cartData.options_total);\n      app.toggleElementClassIf(app.totalDiscount, 'discounted', 'hidden', function () {\n        return !!cartData.total_discount;\n      }).toggleElementClassIf(app.shippingCost, 'has_shipping', 'hidden', function () {\n        return !!cartData.real_shipping_cost;\n      }).toggleElementClassIf(app.freeShipping, 'has_free', 'hidden', function () {\n        return !!cartData.free_shipping_bar;\n      });\n      app.totalDiscount.querySelector('b').innerHTML = '- ' + salla.money(cartData.total_discount);\n      app.shippingCost.querySelector('b').innerHTML = salla.money(cartData.real_shipping_cost);\n      if (!cartData.free_shipping_bar) {\n        return;\n      }\n      var isFree = cartData.free_shipping_bar.has_free_shipping;\n      app.toggleElementClassIf(app.freeShippingBar, 'active', 'hidden', function () {\n        return !isFree;\n      }).toggleElementClassIf(app.freeShipApplied, 'active', 'hidden', function () {\n        return isFree;\n      });\n      app.freeShippingMsg.innerHTML = isFree ? salla.lang.get('pages.cart.has_free_shipping') : salla.lang.get('pages.cart.free_shipping_alert', {\n        amount: salla.money(cartData.free_shipping_bar.remaining)\n      });\n      app.freeShippingBar.children[0].style.width = cartData.free_shipping_bar.percent + '%';\n    }\n\n    /**\r\n     * @param {import(\"@salla.sa/twilight/types/api/cart\").CartItem} item\r\n     */\n  }, {\n    key: \"updateItemInfo\",\n    value: function updateItemInfo(item) {\n      var _item$detailed_offers, _item$detailed_offers2;\n      // lets get the elements for this item\n      var cartItem = document.querySelector('#item-' + item.id);\n      if (!cartItem) {\n        salla.log(\"Can't get the cart item dom for \".concat(item.id, \"!\"));\n        return;\n      }\n      var totalElement = cartItem.querySelector('.item-total'),\n        priceElement = cartItem.querySelector('.item-price'),\n        regularPriceElement = cartItem.querySelector('.item-regular-price'),\n        offerElement = cartItem.querySelector('.offer-name'),\n        oldOffers = cartItem.querySelector('.old-offers'),\n        freeRibbon = cartItem.querySelector('.free-ribbon'),\n        offerIconElement = cartItem.querySelector('.offer-icon'),\n        hasSpecialPrice = item.offer || item.special_price > 0,\n        newOffersActive = ((_item$detailed_offers = item.detailed_offers) === null || _item$detailed_offers === void 0 ? void 0 : _item$detailed_offers.length) > 0;\n      var item_total = ((_item$detailed_offers2 = item.detailed_offers) === null || _item$detailed_offers2 === void 0 ? void 0 : _item$detailed_offers2.length) > 0 ? item.total_special_price : item.total;\n      var total = salla.money(item_total);\n      if (total !== totalElement.innerHTML) {\n        totalElement.innerHTML = total;\n        app.anime(totalElement, {\n          scale: [.88, 1]\n        });\n      }\n      app.toggleElementClassIf([offerElement, oldOffers], 'offer-applied', 'hidden', function () {\n        return hasSpecialPrice && !newOffersActive;\n      }).toggleElementClassIf([offerIconElement, regularPriceElement], 'offer-applied', 'hidden', function () {\n        return hasSpecialPrice;\n      }).toggleElementClassIf(priceElement, 'text-red-400', 'text-sm text-gray-400', function () {\n        return hasSpecialPrice;\n      }).toggleElementClassIf(freeRibbon, 'active', 'hidden', function () {\n        return item.price == 0;\n      });\n      priceElement.innerHTML = salla.money(item.price);\n      if (!hasSpecialPrice) {\n        return;\n      }\n      if (!newOffersActive) {\n        offerElement.innerHTML = item.offer.names;\n      }\n      regularPriceElement.innerHTML = salla.money(item.product_price);\n    }\n    //=================== Coupon Method ========================//\n  }, {\n    key: \"initiateCoupon\",\n    value: function initiateCoupon() {\n      var _this3 = this;\n      if (!app.couponCodeInput) {\n        return;\n      }\n      app.onKeyUp(app.couponCodeInput, function (event) {\n        event.keyCode === 13 && app.couponBtn.click();\n        app.couponError.value = '';\n        app.removeClass(app.couponCodeInput, 'has-error');\n      });\n      app.onClick(app.couponBtn, function (event) {\n        //if it's remove coupon, will have `btn--danger` class\n        var hasCoupon = app.couponBtn.classList.contains('btn--danger');\n        /** @type HTMLSallaButtonElement */\n        var btn = event.currentTarget;\n        if (!hasCoupon && !app.couponCodeInput.value.length) {\n          _this3.showCouponError('* ' + salla.lang.get('pages.checkout.enter_coupon'));\n          return;\n        }\n        btn.load().then(function () {\n          return hasCoupon ? salla.cart.deleteCoupon() : salla.cart.addCoupon(app.couponCodeInput.value);\n        }).then(function (res) {\n          return _this3.toggleCoupon(res, !hasCoupon);\n        })[\"catch\"](function (err) {\n          var _err$response;\n          return _this3.showCouponError((_err$response = err.response) === null || _err$response === void 0 || (_err$response = _err$response.data) === null || _err$response === void 0 ? void 0 : _err$response.error.message, !hasCoupon);\n        })[\"finally\"](function () {\n          return btn.stop();\n        });\n      });\n    }\n\n    /**\r\n     * @param {CartResponse.update} res\r\n     * @param {boolean} applied\r\n     */\n  }, {\n    key: \"toggleCoupon\",\n    value: function toggleCoupon(_res, applied) {\n      app.couponError.innerText = '';\n      app.couponCodeInput.value = applied ? app.couponCodeInput.value : '';\n      app.couponCodeInput.toggleAttribute('disabled', applied);\n      app.toggleElementClassIf(app.couponBtn, ['btn--danger', 'has-coupon'], ['btn-default', 'has-not-coupon'], function () {\n        return applied;\n      }).toggleElementClassIf(app.couponBtn, ['btn-default', 'has-not-coupon'], ['btn--danger', 'has-coupon'], function () {\n        return !applied;\n      }).hideElement(app.couponBtn.querySelector(applied ? 'span' : 'i')).showElement(app.couponBtn.querySelector(applied ? 'i' : 'span')).removeClass(app.couponCodeInput, 'has-error');\n    }\n  }, {\n    key: \"showCouponError\",\n    value: function showCouponError(message) {\n      var isApplying = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      app.couponError.innerText = message || salla.lang.get('pages.checkout.error_occurred');\n      isApplying ? app.addClass(app.couponCodeInput, 'has-error') : null;\n    }\n  }]);\n}(_base_page__WEBPACK_IMPORTED_MODULE_5__[\"default\"]);\nCart.initiateWhenReady(['cart']);\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/cart.js?");

/***/ }),

/***/ "./src/assets/js/partials/validate-product-options.js":
/*!************************************************************!*\
  !*** ./src/assets/js/partials/validate-product-options.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateProductOptions: () => (/* binding */ validateProductOptions)\n/* harmony export */ });\n/**\r\n * Validates product options for items in the cart.\r\n */\nfunction validateProductOptions() {\n  var cartItems = document.querySelectorAll('.main-content form:not(.cart-options form)');\n  if (!cartItems.length) return;\n  cartItems.forEach(function (item) {\n    var itemId = getItemId(item);\n    var productOptions = item.querySelector('salla-product-options');\n    var quantityComponent = item.querySelector('salla-quantity-input');\n    if (quantityComponent) {\n      observeQuantityChanges(quantityComponent, itemId, item);\n    }\n\n    // Listen for product options changes\n    if (productOptions) {\n      productOptions.addEventListener('changed', function (e) {\n        setTimeout(function () {\n          var _e$detail, _e$detail2;\n          if (!item.reportValidity() || ((_e$detail = e.detail) === null || _e$detail === void 0 || (_e$detail = _e$detail.event) === null || _e$detail === void 0 ? void 0 : _e$detail.type) == 'added') return;\n          if (Number(itemId) === Number((_e$detail2 = e.detail) === null || _e$detail2 === void 0 ? void 0 : _e$detail2.productId)) {\n            var _e$detail3;\n            appendLoadingOverlay((_e$detail3 = e.detail) === null || _e$detail3 === void 0 ? void 0 : _e$detail3.productId);\n          }\n        }, 100);\n      });\n    }\n  });\n\n  // Event handlers for cart updates\n  salla.cart.event.onItemUpdated(function (_data, id) {\n    removeLoadingOverlay(id);\n  });\n  salla.cart.event.onItemUpdatedFailed(function (_data, itemId) {\n    handleCartUpdateFailure(itemId, cartItems);\n  });\n}\n\n/**\r\n * Observes changes in quantity input for a specific cart item.\r\n */\nfunction observeQuantityChanges(quantityComponent, itemId, item) {\n  var observer = new MutationObserver(function () {\n    var quantityInput = quantityComponent.querySelector('input[name=\"quantity\"]');\n    if (quantityInput) {\n      observer.disconnect(); // Stop observing once input is found\n      quantityInput.addEventListener('change', function (e) {\n        var _e$detail4;\n        if (!item.reportValidity()) return;\n        if (Number(itemId) === Number((_e$detail4 = e.detail) === null || _e$detail4 === void 0 ? void 0 : _e$detail4.productId)) {\n          var _e$detail5;\n          appendLoadingOverlay((_e$detail5 = e.detail) === null || _e$detail5 === void 0 ? void 0 : _e$detail5.productId);\n        }\n      });\n    }\n  });\n  observer.observe(quantityComponent, {\n    childList: true,\n    subtree: true\n  });\n}\n\n/**\r\n * Handles cart update failures by restoring the item state.\r\n */\nfunction handleCartUpdateFailure(itemId, cartItems) {\n  return salla.api.cart.getCurrentCartId(false, \"salla-product-options\").then(function (cartId) {\n    return salla.cart.details(cartId, ['options']);\n  }).then(function (_ref) {\n    var cartDetails = _ref.data.cart;\n    var currentProduct = cartDetails.items.find(function (item) {\n      return Number(item.id) === Number(itemId);\n    });\n    if (!currentProduct) throw new Error(\"Product with ID \".concat(itemId, \" not found in cart details.\"));\n    updateCartItemState(cartItems, currentProduct);\n  }).then(function () {\n    return removeLoadingOverlay();\n  })[\"catch\"](function (error) {\n    console.error(\"Error restoring cart item state:\", error);\n    removeLoadingOverlay();\n  });\n}\n\n/**\r\n * Updates the UI for a specific cart item based on its current state.\r\n */\nfunction updateCartItemState(cartItems, currentProduct) {\n  cartItems.forEach(function (item) {\n    var ID = getItemId(item);\n    if (Number(currentProduct.id) === Number(ID)) {\n      var productOptions = item.querySelector('salla-product-options');\n      var quantityInput = item.querySelector('salla-quantity-input');\n      if (productOptions) productOptions.setOptionsData(currentProduct.options, false);\n      if (quantityInput) quantityInput.setValue(currentProduct.quantity, false);\n    }\n  });\n}\n\n/**\r\n * Appends a loading overlay to the cart item with the given ID.\r\n */\nfunction appendLoadingOverlay(itemId) {\n  if (!itemId) return;\n  var loadingOverlay = createLoadingOverlay();\n  var parentElement = document.querySelector(\"#item-\".concat(itemId, \" .cart-item\"));\n  if (parentElement) {\n    parentElement.appendChild(loadingOverlay);\n  }\n}\n\n/**\r\n * Removes the loading overlay from a specific cart item or all items if no ID is provided.\r\n */\nfunction removeLoadingOverlay(itemId) {\n  var targetItems = itemId ? [document.querySelector(\".main-content form:not(.cart-options form)#item-\".concat(itemId, \" .cart-item\"))] : document.querySelectorAll('.main-content form:not(.cart-options form) .cart-item');\n  targetItems.forEach(function (item) {\n    var loadingOverlay = item === null || item === void 0 ? void 0 : item.querySelector('.loading-overlay');\n    if (loadingOverlay) {\n      setTimeout(function () {\n        return loadingOverlay.remove();\n      }, 0);\n    }\n  });\n}\n\n/**\r\n * Creates a loading overlay element.\r\n */\nfunction createLoadingOverlay() {\n  var overlay = document.createElement('div');\n  overlay.classList.add('loading-overlay', 'absolute', 'inset-0', 'z-50', 'flex', 'justify-center', 'items-center');\n  var background = document.createElement('div');\n  background.classList.add('absolute', 'inset-0', 'bg-white', 'opacity-40');\n  var loader = document.createElement('div');\n  loader.innerHTML = '<salla-loading size=\"32\"></salla-loading>';\n  loader.classList.add('relative', 'z-10');\n  overlay.appendChild(background);\n  overlay.appendChild(loader);\n  return overlay;\n}\n\n/**\r\n * Extracts the item ID from a cart item element.\r\n */\nfunction getItemId(item) {\n  var _item$querySelector;\n  return (_item$querySelector = item.querySelector('input[type=\"hidden\"][name=\"id\"]')) === null || _item$querySelector === void 0 ? void 0 : _item$querySelector.value;\n}\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/partials/validate-product-options.js?");

/***/ }),

/***/ "./src/assets/js/thankyou.js":
/*!***********************************!*\
  !*** ./src/assets/js/thankyou.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js\");\n/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"./node_modules/.pnpm/@babel+runtime@7.27.3/node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _base_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./base-page */ \"./src/assets/js/base-page.js\");\n\n\n\n\n\nfunction _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\n\nvar ThankYou = /*#__PURE__*/function (_BasePage) {\n  function ThankYou() {\n    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, ThankYou);\n    return _callSuper(this, ThankYou, arguments);\n  }\n  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(ThankYou, _BasePage);\n  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(ThankYou, [{\n    key: \"onReady\",\n    value: function onReady() {\n      app.anime('.thanks-item', {\n        translateX: [20, 0]\n      });\n      var form = document.querySelector('#invoice-form');\n      salla.order.event.onInvoiceSent(function (res) {\n        form.innerHTML = res.data.message;\n        form.classList.add('sent');\n      });\n    }\n  }]);\n}(_base_page__WEBPACK_IMPORTED_MODULE_5__[\"default\"]);\nThankYou.initiateWhenReady(['thank-you']);\n\n//# sourceURL=webpack://theme-raed/./src/assets/js/thankyou.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	__webpack_require__("./src/assets/js/cart.js");
/******/ 	var __webpack_exports__ = __webpack_require__("./src/assets/js/thankyou.js");
/******/ 	
/******/ })()
;