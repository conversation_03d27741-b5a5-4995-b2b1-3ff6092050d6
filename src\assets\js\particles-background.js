// Gaming Background with Image and Neon Particles
// This script creates a background image with moving neon particles

document.addEventListener('DOMContentLoaded', function() {
  // Create background div for image
  const bgDiv = document.createElement('div');
  bgDiv.id = 'background-image';

  // Add to body as first child to be behind everything
  document.body.insertBefore(bgDiv, document.body.firstChild);

  // Style the background div
  bgDiv.style.position = 'fixed';
  bgDiv.style.top = '0';
  bgDiv.style.left = '0';
  bgDiv.style.width = '100vw';
  bgDiv.style.height = '100vh';
  bgDiv.style.zIndex = '-2';
  bgDiv.style.backgroundImage = 'url("https://img.freepik.com/premium-vector/dark-hexagon-gaming-abstract-vector-background-with-pink-blue-colored-bright-flashes_156943-1454.jpg")';
  bgDiv.style.backgroundSize = 'cover';
  bgDiv.style.backgroundPosition = 'center';
  bgDiv.style.backgroundRepeat = 'no-repeat';
  bgDiv.style.pointerEvents = 'none';
  bgDiv.style.overflow = 'hidden';

  // Create canvas element for particles
  const canvas = document.createElement('canvas');
  canvas.id = 'particles-background';

  // Add canvas after background div
  document.body.insertBefore(canvas, bgDiv.nextSibling);

  // Style the canvas to cover the entire background
  canvas.style.position = 'fixed';
  canvas.style.top = '0';
  canvas.style.left = '0';
  canvas.style.width = '100vw';
  canvas.style.height = '100vh';
  canvas.style.zIndex = '-1';
  canvas.style.pointerEvents = 'none';
  canvas.style.overflow = 'hidden';
  canvas.style.background = 'transparent';

  // Get the canvas context
  const ctx = canvas.getContext('2d');

  // Set canvas to full window size
  const resizeCanvas = () => {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  };

  // Call once to set initial size
  resizeCanvas();

  // Resize canvas when window size changes
  window.addEventListener('resize', resizeCanvas);

  // Configuration
  const config = {
    // Particle settings
    particleCount: Math.min(80, Math.floor((window.innerWidth * window.innerHeight) / 20000)),
    particleBaseSize: 2,
    particleAddedSize: 1.5,
    particleMaxSpeed: 0.8,
    particleColors: [
      'rgba(255, 0, 255, 0.7)',   // Magenta (to match image)
      'rgba(0, 255, 255, 0.7)',   // Cyan (to match image)
      'rgba(128, 0, 255, 0.7)',   // Purple (to match image)
      'rgba(255, 0, 128, 0.7)',   // Pink (to match image)
      'rgba(0, 128, 255, 0.7)'    // Blue (to match image)
    ],

    // Connection settings
    connectionDistance: 120,
    connectionWidth: 0.4,

    // Background settings
    backgroundColor: 'rgba(10, 10, 24, 0.03)', // Very subtle overlay

    // Glow settings
    glowBlur: 20,
    glowStrength: 0.6
  };

  // Particle class
  class Particle {
    constructor() {
      this.reset();
    }

    reset() {
      this.x = Math.random() * canvas.width;
      this.y = Math.random() * canvas.height;
      this.size = Math.random() * config.particleAddedSize + config.particleBaseSize;
      this.speedX = (Math.random() - 0.5) * config.particleMaxSpeed;
      this.speedY = (Math.random() - 0.5) * config.particleMaxSpeed;
      this.color = config.particleColors[Math.floor(Math.random() * config.particleColors.length)];
      this.pulseSpeed = Math.random() * 0.01 + 0.01;
      this.pulseDirection = Math.random() > 0.5 ? 1 : -1;
      this.pulseAmount = 0;
      this.originalSize = this.size;
      this.glowSize = this.size * 3;
    }

    update() {
      // Move particle
      this.x += this.speedX;
      this.y += this.speedY;

      // Pulse size effect
      this.pulseAmount += this.pulseSpeed * this.pulseDirection;
      if (Math.abs(this.pulseAmount) > 0.5) {
        this.pulseDirection *= -1;
      }
      this.size = this.originalSize + this.pulseAmount;

      // Reset particle if it goes off screen
      if (this.x < 0 || this.x > canvas.width || this.y < 0 || this.y > canvas.height) {
        this.reset();
      }
    }

    draw() {
      // Draw glow
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.glowSize, 0, Math.PI * 2);
      const gradient = ctx.createRadialGradient(
        this.x, this.y, 0,
        this.x, this.y, this.glowSize
      );
      gradient.addColorStop(0, this.color);
      gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
      ctx.fillStyle = gradient;
      ctx.fill();

      // Draw particle
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
      ctx.fillStyle = this.color;
      ctx.fill();
    }
  }

  // Create particles
  const particles = [];
  for (let i = 0; i < config.particleCount; i++) {
    particles.push(new Particle());
  }

  // No grid drawing function needed as we're using a background image

  // Draw connections between particles
  function drawConnections() {
    for (let i = 0; i < particles.length; i++) {
      for (let j = i + 1; j < particles.length; j++) {
        const dx = particles[i].x - particles[j].x;
        const dy = particles[i].y - particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < config.connectionDistance) {
          // Calculate line opacity based on distance
          const opacity = 1 - (distance / config.connectionDistance);

          ctx.beginPath();
          ctx.moveTo(particles[i].x, particles[i].y);
          ctx.lineTo(particles[j].x, particles[j].y);
          ctx.strokeStyle = `rgba(29, 233, 182, ${opacity * 0.2})`;
          ctx.lineWidth = config.connectionWidth;
          ctx.stroke();
        }
      }
    }
  }

  // Animation function
  function animate() {
    // Clear canvas with semi-transparent background for trail effect
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = config.backgroundColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw connections between particles
    drawConnections();

    // Update and draw particles
    particles.forEach(particle => {
      particle.update();
      particle.draw();
    });

    // Request next frame
    requestAnimationFrame(animate);
  }

  // Start animation
  animate();
});