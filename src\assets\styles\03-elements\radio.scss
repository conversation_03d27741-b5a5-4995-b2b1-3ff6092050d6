.custom-radios {
  div {
    display: inline-block;
  }

  input[type="radio"] {
    display: none;

    + label {
      cursor: pointer;

      span {
        display: inline-block;
        width: 40px;
        height: 40px;
        // margin: -1px 4px 0 0;
        vertical-align: middle;
        cursor: pointer;
        border-radius: 6px;
        border: 2px solid #ffffff;
        background-repeat: no-repeat;
        background-position: center;
        text-align: center;
        line-height: 40px;
        box-shadow: 0 1px 3px 0 rgba(#000, 0.075);

        &::after {
          opacity: 0;
          display: inline-block;
          transform: scale(0.4);
          transition: all 0.3s ease;
          font-family: "sallaicons" !important;
          // speak: never;
          font-style: normal;
          font-weight: normal;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          -webkit-font-smoothing: antialiased;
          content: "\ea9d";
          color: #fff;
          font-size: 20px;
          font-weight: bold;
        }
      }
    }

    &:checked + label span:after {
      opacity: 1;
      transform: scale(1);
    }

    &:disabled + label {
      opacity: .45;
    }
  }

  &--rounded {
    input[type="radio"] {
      + label {
        span {
          border-radius: 50%;
          width: 30px;
          height: 30px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          background-color: #ddd;

          &::after {
            content: "";
            width: 14px;
            height: 14px;
            background-color: #f1f1f1;
            border-radius: 50%;
          }
        }
      }
    }
  }

  &--icons {
    font-size: 40px;
    line-height: 42px;

    input[type="radio"] {
      + label {
        transition: transform 0.2s, color 0.3s;
        filter: grayscale(1);


        &:hover {
          filter: grayscale(0);
        }
      }

      &:checked + label {
        filter: grayscale(0);
        transform: scale(1.25) translateY(-3px);

        .rate-title {
          font-weight: bold;
          color: #2b2d34;
        }
      }
    }
  }
}