{"name": {"ar": "any", "en": "any"}, "repository": "https://github.com/fady-eng/any", "author_email": "<EMAIL>", "features": ["mega-menu", "fonts", "color", "breadcrumb", "unite-cards-height", "component-featured-products", "component-fixed-banner", "component-fixed-products", "component-products-slider", "component-photos-slider", "component-parallax-background", "component-testimonials", "component-square-photos", "component-store-features", "component-youtube", "menu-images", "filters"], "settings": [{"id": "squar_photo_bg_image_size", "type": "items", "format": "dropdown-list", "label": "طريقة عرض الصور في قسم قائمة عناصر ", "description": null, "labelHTML": null, "icon": "sicon-list", "selected": [{"label": "اظهار الصورة كاملةً في المنتصف (Contain)", "value": "contain", "key": "f4adec13-6e4c-654b-b978-b2807935ced9"}], "options": [{"label": "تغطية الصورة كامل المساحة مع المحافظة على أبعاد الصورة (Cover)", "value": "cover", "key": "e9c8ea8f-c5c6-234e-935-3bce269afde4"}, {"label": "اظهار الصورة كاملةً في المنتصف (Contain)", "value": "contain", "key": "f4adec13-6e4c-654b-b978-b2807935ced9"}], "source": "Manual", "required": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "وضع عمودي للمنتجات في مربع المنتجات الثابتة في الصفحة الرئيسية", "description": null, "id": "vertical_fixed_products", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "boolean", "id": "is_more_button_enabled", "label": "عرض (زر الكل) في الصفحة الرئيسية", "format": "switch"}, {"type": "static", "id": "static-line1", "format": "line"}, {"type": "static", "format": "title", "id": "static-label2", "value": "خيارات أعلى الصفحة", "variant": "h6"}, {"id": "header_is_sticky", "label": "تثبت القائمة الرئيسية أعلى الصفحة عند التمرير لأسفل", "description": "يُنصح بالغاء تفعيلها في حالة وجود عناصر كثيرة في القائمة المنسدلة", "type": "boolean", "icon": "sicon-toggle-off", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "شريط علوي داكن", "description": null, "id": "topnav_is_dark", "format": "switch", "required": false, "value": true, "selected": false}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "عرض روابط الصفحات الهامة في الشريط العلوي", "description": "مثل صفحة الهبوط ، المدونة ،الصفحات التعريفية", "id": "important_links", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "static", "id": "static-line3", "format": "line"}, {"type": "static", "format": "title", "id": "static-label4", "value": "خيارات أسفل الصفحة", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "الوضع الداكن", "description": null, "id": "footer_is_dark", "format": "switch", "required": false, "value": true, "selected": false}, {"type": "static", "id": "static-line5", "format": "line"}, {"type": "static", "format": "title", "id": "static-label6", "value": "خيارات صفحة المنتج", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تثبيت زر الإضافة والكمية أسفل شاشة الجوال", "description": null, "id": "sticky_add_to_cart", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "اظهار الوسوم", "description": null, "id": "show_tags", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "slider_background_size", "type": "items", "format": "dropdown-list", "label": "طريقة عرض الصور في سليدر صور المنتج", "description": null, "labelHTML": null, "icon": "sicon-list", "selected": [{"label": "اظهار الصورة كاملةً في المنتصف (Contain)", "value": "contain", "key": "f4adec13-6e4c-433b-b141-b2807935ced9"}], "options": [{"label": "تغطية الصورة كامل المساحة مع المحافظة على أبعاد الصورة (Cover)", "value": "cover", "key": "e9c8ea8f-c5c6-438e-9d45-3bce269afde4"}, {"label": "اظهار الصورة كاملةً في المنتصف (Contain)", "value": "contain", "key": "f4adec13-6e4c-433b-b141-b2807935ced9"}], "source": "Manual", "required": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تكبير الصور في سليدر صور المنتج", "description": "هذه الخاصية تمكنك من تكبير الصورة لتسهل رؤية المزيد من التفاصيل بها", "id": "imageZoom", "format": "switch", "required": false, "value": false, "selected": false}], "components": [{"key": "186b3f4f-25cf-4d3c-abca-cef7eed6f0ab", "title": "صور متحركة (محسنة)", "icon": "sicon-image-carousel", "image": "https://cdn.salla.sa/mQgZlG/K36XDnXG3odwQs7Xzy68IpM2wNuJ7uTImfBInlQ8.png", "path": "home.enhanced-slider", "is_default": true, "fields": [{"type": "static", "format": "description", "id": "static-desc", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/images-slider-enhancement.png?v=1.1'></div>"}, {"type": "static", "format": "description", "id": "enhance-slider-note", "value": "<div style='background-color: #ddedff;color: #0a5a90;padding: 16px 20px;font-size: 14px;border-radius: 4px; margin: 0 0 30px;'>هذا العنصر غير مناسب لعرض البنرات، يمكنك استخدام عنصر <strong>صور متحركة</strong> بدلاً منه لتظهر صورة البنر بشكل كامل</div>"}, {"id": "slides", "type": "collection", "format": "collection", "required": true, "minLength": 1, "maxLength": 10, "label": null, "item_label": "صورة", "value": [{"slides.image": "https://cdn.salla.sa/ZrBEO/MopzhoqpTkDObcKc8Q8n77AuydanfsGXdZO7iinH.jpg", "slides.title": "روِّج لمنتجاتك كصور متحركة", "slides.description": "اعرض المنتجات التي تود إبرازها لزوّار متجرك في هذه المساحة"}, {"slides.image": "https://cdn.salla.sa/ZrBEO/3gOhYSaIhaHxhH2IHWxJYDl9JHwxP6dGKiZFSq4K.jpg", "slides.title": "شجّع زوار متجرك على الشراء عبر عروض خاصة", "slides.description": "اعرض المنتجات التي تود إبرازها لزوّار متجرك في هذه المساحة"}], "fields": [{"type": "string", "icon": "sicon-image", "value": null, "id": "slides.image", "label": "صورة البنر", "format": "image", "required": true, "placeholder": null, "description": "* المقاس المناسب للصورة هو 900×600 بكسل"}, {"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان رئيسي", "multilanguage": true, "id": "slides.title", "value": null, "required": false, "format": "text", "description": "يتم عرضه على الصورة بحجم بارز جداً، مع إضافة تأثير دخول للنص يضيف لمسة جمالية على معرض الصور.", "placeholder": "يمكنك إضافة عنوان بارز هنا...", "hide": false, "minLength": 0, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "نص إضافي", "multilanguage": true, "id": "slides.description", "value": null, "description": "يتم عرضه على الصورة بحجم أصغر من العنوان الرئيسي، مع إضافة تأثير دخول للنص يضيف لمسة جمالية على معرض الصور.", "format": "textarea", "required": false, "placeholder": "يمكنك إضافة تفاصيل إضافية هنا", "minLength": 0, "maxLength": 255}]}]}, {"key": "2b1b130b-5b37-422a-9683-e0fd367460c0", "title": "روابط سريعة", "icon": "sicon-layout-grid-rearrange", "path": "home.main-links", "image": "https://cdn.salla.sa/mQgZlG/c3AwZKb12ZRFFfK3vE0vaZYTePnFAUOiZA4dQBg1.png", "is_default": true, "fields": [{"type": "static", "format": "description", "id": "with-bg", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/main-links-with-bg.jpg?v=1.1'></div>", "conditions": [{"id": "merge_with_top_component", "operation": "=", "value": true}]}, {"type": "static", "format": "description", "id": "without-bg", "value": "<div style='padding-top:10px;margin-bottom:30px;border:1px solid #ebebeb;border-radius:5px;text-align:center;overflow:hidden'><h6 style='margin-bottom:10px'>معاينة العنصر</h6><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/main-links.png?v=1.1'></div>", "conditions": [{"id": "merge_with_top_component", "operation": "=", "value": false}]}, {"type": "string", "icon": "sicon-format-text-alt", "label": "العنوان", "multilanguage": true, "id": "title", "value": null, "required": false, "format": "text", "description": null, "placeholder": "أدخل العنوان هنا...", "minLength": 0, "maxLength": 100}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "دمج مع العنصر السابق", "description": null, "id": "merge_with_top_component", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "static", "format": "description", "id": "static-desc", "value": "<small style='margin: -36px 0 30px;display: block;'>* إزاحة كامل العنصر ومحتوياته مع العنصر السابق، وجعلهما يظهران كعنصر واحد متناسق، يكون ذا فائدة في حالة كان العنصر السابق هو (صور متحركة محسنة). </small>"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "عرض أسهم التحرك", "description": "", "id": "show_controls", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "عرض روابط التصنيفات", "description": "يمكنك تحديد التصنيفات بدل العناصر الخاصة، مع امكانية عرض صورة التصنيف مع العنصر", "id": "show_cats", "format": "switch", "required": false, "value": false, "selected": false}, {"id": "categories", "type": "items", "format": "dropdown-list", "label": "<PERSON><PERSON><PERSON> التصنيفات", "labelHTML": null, "icon": "sicon-keyboard_arrow_down", "selected": [], "options": [], "source": "categories", "multichoice": true, "searchable": true, "required": false, "conditions": [{"id": "show_cats", "operation": "=", "value": true}]}, {"id": "links", "type": "collection", "format": "collection", "required": true, "minLength": 3, "maxLength": 100, "item_label": "رابط ", "value": [{"links.icon": "sicon-packed-box", "links.title": "منتج جاهز", "links.url__type": "offers_link"}, {"links.icon": "sicon-fabric-swatch", "links.title": "خد<PERSON>ة حسب الطلب", "links.url__type": "offers_link"}, {"links.icon": "sicon-cake", "links.title": "أكل ومشروبات", "links.url__type": "offers_link"}, {"links.icon": "sicon-game-controller-alt", "links.title": "منتج رقمي", "links.url__type": "offers_link"}, {"links.icon": "sicon-barcode-scan", "links.title": "بطاقة رقمية", "links.url__type": "offers_link"}, {"links.icon": "sicon-inbox-full", "links.title": "مجموعة منتجات", "links.url__type": "offers_link"}, {"links.icon": "sicon-calendar-date", "links.title": "حجوزات", "links.url__type": "offers_link"}], "fields": [{"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان الرابط", "id": "links.icon", "value": "sicon-store2", "required": false, "format": "icon", "class": "form--inline", "description": null}, {"type": "string", "icon": "sicon-format-text-alt", "multilanguage": true, "id": "links.title", "value": null, "required": false, "format": "text", "description": null, "placeholder": "أدخل عنوان للرابط هنا...", "minLength": 2, "maxLength": 80}, {"type": "items", "icon": "sicon-link", "label": "الرابط", "id": "links.url", "value": null, "description": null, "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}], "conditions": [{"id": "show_cats", "operation": "=", "value": false}]}]}, {"key": "9a758d20-2ce4-4782-91fe-c04466464588", "title": "منتجات متحركة مع خلفية", "icon": "sicon-list-play", "path": "home.slider-products-with-header", "image": "https://cdn.salla.sa/mQgZlG/3hhreht06MIPFWepyuz42rg3bHqGZIYmDVs1PjYc.png", "fields": [{"type": "static", "format": "description", "id": "with-bg", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/slider-products-with-bg.png?v=1.1'></div>"}, {"type": "string", "icon": "sicon-image", "value": "https://cdn.salla.sa/form-builder/D7bG3pgSit43TheOejBOZV7LIfG1y1Cj0hWLmgIh.jpg", "label": "صورة الخلفية", "id": "background", "format": "image", "required": true, "placeholder": null, "description": "* المقاس المناسب للصورة هو 1233×500 بكسل", "settings": {"height": 580, "width": 1400}}, {"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان رئيسي", "multilanguage": true, "id": "title", "value": null, "required": false, "format": "text", "description": null, "placeholder": "يمكنك إضافة العنوان الرئيسي هنا...", "hide": false, "minLength": 0, "maxLength": "80"}, {"type": "string", "icon": "sicon-typography", "label": "نص توضيحي", "multilanguage": true, "id": "description", "value": null, "description": null, "format": "textarea", "required": false, "placeholder": "يمكنك إضافة وصف لهذا القسم هنا...", "minLength": 0, "maxLength": "256"}, {"type": "items", "icon": "sicon-keyboard_arrow_down", "label": "منتجات", "id": "products", "format": "dropdown-list", "description": null, "selected": [], "options": [], "required": true, "multichoice": true, "source": "products", "searchable": true, "maxLength": 8, "minLength": 1, "value": []}, {"type": "items", "icon": "sicon-link", "label": "رابط عرض الكل", "id": "display_all_url", "value": [], "description": "عند إختيار رابط فارغ سيتم إخفاء زر 'عرض الكل'", "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}]}, {"key": "b89edc4a-ce0b-468f-8323-2da48147bb32", "title": "صور مربعة (محسنة)", "icon": "sicon-image", "path": "home.enhanced-square-banners", "image": "https://cdn.salla.sa/mQgZlG/VSk26LArCczWj085xH8jxuusMiKzrcE1wsVC6pLm.png", "is_default": true, "fields": [{"type": "static", "format": "description", "id": "with-bg", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/square-images.png?v=1.1'></div>"}, {"id": "banners", "type": "collection", "format": "collection", "required": true, "minLength": 1, "maxLength": 5, "label": "قائمة الصور", "item_label": "صورة ", "value": [{"banners.image": "https://cdn.salla.sa/mQgZlG/pzOCEw10wCfXeVSpKnL0Ifd1O9ZkTfnZ17RkE14R.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}, {"banners.image": "https://cdn.salla.sa/mQgZlG/lkyDjYwvWJNmAQoGnLALLcUkm66rCCDbTyVlSGdH.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}, {"banners.image": "https://cdn.salla.sa/mQgZlG/lkyDjYwvWJNmAQoGnLALLcUkm66rCCDbTyVlSGdH.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}, {"banners.image": "https://cdn.salla.sa/mQgZlG/lkyDjYwvWJNmAQoGnLALLcUkm66rCCDbTyVlSGdH.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}, {"banners.image": "https://cdn.salla.sa/mQgZlG/lkyDjYwvWJNmAQoGnLALLcUkm66rCCDbTyVlSGdH.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}], "fields": [{"type": "string", "icon": "sicon-image", "value": null, "label": "صورة البنر", "id": "banners.image", "format": "image", "required": true, "placeholder": "", "description": "* المقاس المناسب للصورة هو 640×427 بكسل", "settings": {"height": 427, "width": 640}}, {"type": "items", "icon": "sicon-link", "label": "الرابط", "id": "banners.url", "value": null, "description": "", "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}, {"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان رئيسي", "multilanguage": true, "id": "banners.title", "value": null, "required": false, "format": "text", "description": "نص يتم إضافته على الصورة بنمط خط كبير", "placeholder": "أدخل النص هنا...", "hide": false, "minLength": 0, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "نص توضيحي", "multilanguage": true, "id": "banners.description", "value": null, "description": "نص يتم إضافته على الصورة بخط أصغر من العنوان الرئيسي", "format": "textarea", "required": false, "placeholder": "Your text here", "minLength": 0, "maxLength": 255}]}]}, {"key": "25f6cf26-a53f-4954-9b32-739b311b32c7", "title": "الماركات التجارية", "icon": "sicon-award-ribbon", "path": "home.brands", "image": "https://cdn.salla.sa/mQgZlG/kvFhVeuUyjK4nHUovBQBZ632Hb6gKV2BXZwid40e.png", "fields": [{"type": "static", "format": "description", "id": "with-bg", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/brands.png?v=1.1'></div>"}, {"type": "string", "icon": "sicon-format-text-alt", "label": "العنوان", "multilanguage": true, "id": "title", "required": false, "format": "text", "description": "", "placeholder": "أدخل العنوان هنا...", "minLength": 0, "maxLength": 200, "value": {"ar": "تصفح من خلال العلامات التجارية", "en": "Browse All Brands"}}, {"type": "items", "icon": "sicon-keyboard_arrow_down", "label": "الماركات التجارية", "id": "brands", "format": "dropdown-list", "description": "", "selected": [], "options": [], "required": true, "multichoice": true, "searchable": true, "source": "brands"}]}, {"key": "541cc423-90c7-4230-8a33-a0342cfde4ad", "title": "آراء عملاء مخصصة", "icon": "sicon-chat-bubbles", "path": "home.custom-testimonials", "image": "https://cdn.salla.sa/mQgZlG/JMJiEx1KVn7mzxo5FdtsVsAmpWPM8UJW0i0B93c0.png", "is_default": true, "fields": [{"type": "static", "format": "description", "id": "static-desc", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/custom-testimonials.png?v=1.1'></div>"}, {"id": "items", "key": "0473eb46-c7f5-44c5-bd6f-b1fe716a5e64", "type": "collection", "format": "collection", "label": "التقييمات", "description": null, "labelHTML": null, "item_label": "التقييم", "icon": "sicon-list-add", "value": [{"items.name": "ج<PERSON><PERSON><PERSON> عبد الله ", "items.avatar": "https://cdn.assets.salla.network/prod/stores/themes/default/assets/images/avatar_male.png", "items.stars": 4, "items.text": "تجربة تسوق رائعة مع متجركم، المنتجات عالية الجودة والأسعار منافسة. خدمة العملاء ممتازة والتوصيل سريع. أنصح الجميع بالتسوق من هذا المتجر."}, {"items.name": "ج<PERSON><PERSON><PERSON> عبد الله ", "items.avatar": "https://cdn.assets.salla.network/prod/stores/themes/default/assets/images/avatar_male.png", "items.stars": 3, "items.text": "متجر مميز بكل المقاييس، التنوع في المنتجات كبير والأسعار معقولة. التوصيل وصل في الوقت المحدد والمنتجات مطابقة للوصف."}, {"items.name": "ج<PERSON><PERSON><PERSON> عبد الله ", "items.avatar": "https://cdn.assets.salla.network/prod/stores/themes/default/assets/images/avatar_male.png", "items.stars": 5, "items.text": "أفضل متجر اشتريت منه، جودة المنتجات فائقة والأسعار مناسبة. خدمة العملاء متعاونة جداً والتوصيل سريع. سأعود للتسوق مرة أخرى."}, {"items.name": "ج<PERSON><PERSON><PERSON> عبد الله ", "items.avatar": "https://cdn.assets.salla.network/prod/stores/themes/default/assets/images/avatar_male.png", "items.stars": 5, "items.text": "تجربة تسوق مميزة جداً، المنتجات متنوعة والأسعار معقولة. التوصيل سريع والمنتجات مطابقة للوصف. أنصح الجميع بالتسوق من هذا المتجر."}], "fields": [{"id": "items.name", "key": "04e34d04-d1c3-4b75-b1d6-4e5b4ed22623", "type": "string", "format": "text", "label": "اسم العميل", "description": null, "labelHTML": null, "placeholder": "اسم العميل", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false}, {"id": "items.avatar", "key": "9ae4a6cc-7d20-4ae9-8bc0-33b2452502ca", "type": "string", "format": "image", "label": "صورة العميل", "description": "المقاس المناسب للصورة هو 68*68 بكسل", "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": true}, {"id": "items.stars", "type": "number", "format": "integer", "label": "<PERSON><PERSON><PERSON> النجوم", "description": null, "key": "f0caf793-88f8-4178-b1cc-a7ace5c09ff6", "labelHTML": "من 1 إلى 5", "placeholder": "القيمة", "icon": "sicon-pencil-ruler", "value": 5, "required": false, "minimum": 1, "maximum": 5}, {"id": "items.text", "key": "0d474cc4-39f6-4495-8e24-a8eb10d4bbab", "type": "string", "format": "textarea", "label": "نص التقييم", "description": null, "labelHTML": null, "placeholder": "نص التقييم", "icon": "sicon-format-text-alt", "value": null, "multilanguage": true, "required": false}], "required": false, "minLength": 1, "maxLength": 30}]}, {"key": "2c1f2c6f-49df-4243-a37f-3112b0cd38ce", "title": "معرض مع صور مصغرة", "icon": "sicon-delete_col", "path": "home.firstGallary", "fields": [{"id": "banner", "type": "string", "format": "image", "label": "صورة الخلفية او البنر", "description": null, "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "title", "type": "string", "format": "text", "label": "عنوان البنر", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": 6, "maxLength": 30}, {"id": "description", "type": "string", "format": "text", "label": "وصف البنر", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": 6, "maxLength": 30}, {"id": "icon", "type": "collection", "format": "collection", "label": "الصور المصغرة", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "icon.img", "type": "string", "format": "image", "label": "صورة", "description": null, "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}], "value": [], "minLength": 1, "maxLength": "10"}]}, {"key": "534708ba-2a94-491c-8ba5-91f2f9b9128d", "title": "خط فاصل مميز", "icon": "sicon-store", "path": "home.lineBreak", "fields": [{"id": "number", "type": "number", "format": "integer", "inputType": "number", "label": "رقم", "description": null, "labelHTML": null, "placeholder": "Input an integer number between 5 and 50.", "icon": "sicon-hashtag", "value": null, "required": false, "minimum": 0, "maximum": 50}]}, {"key": "7d0bb221-a426-4a61-bf92-b5faa6c6e99a", "title": "شاشة تحميل للمتجر", "icon": "sicon-store", "path": "home.loadingScreen", "fields": [{"id": "title", "type": "static", "format": "title", "label": "Title", "value": "اختر شاشة تحميل للمتجر", "variant": "h1", "icon": "sicon-format-text"}, {"id": "image", "type": "string", "format": "image", "label": "Image", "description": null, "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}]}, {"key": "bab03ed3-71fd-4972-9f3a-86e90b356be9", "title": "معرض مميز 2", "icon": "sicon-store", "path": "home.secondGallary", "fields": [{"id": "title", "type": "string", "format": "text", "label": "عنوان", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": "ادخل عنوان القسم", "multilanguage": false, "required": false, "minLength": "1", "maxLength": 30}, {"id": "icon", "type": "collection", "format": "collection", "label": "icon", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "icon.image", "type": "string", "format": "image", "label": "صورة", "description": null, "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "icon.title", "type": "string", "format": "text", "label": "عنوان", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "1", "maxLength": "50"}], "value": [], "minLength": 1, "maxLength": "20"}]}, {"key": "d6e5c58f-627e-4888-aa54-bd99ec7255e6", "title": "بنر مع عرض", "icon": "sicon-store", "path": "home.bannerWithOffer", "fields": [{"id": "timer", "type": "collection", "format": "collection", "label": "العد التنازلي للعرض", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "timer.bg", "type": "string", "format": "color", "inputType": "color", "label": "لون الخلفية", "description": null, "labelHTML": null, "icon": "sicon-format-fill", "value": "#00bfe0", "required": false}, {"id": "timer.days", "type": "number", "format": "float", "inputType": "number", "label": "عد<PERSON> ايام العرض", "description": "قم بادخال عدد ايام العرض", "labelHTML": null, "placeholder": null, "icon": "sicon-hashtag", "value": null, "required": false, "step": "1", "minimum": "1", "maximum": "10"}], "value": [], "minLength": 1, "maxLength": "1"}, {"id": "offer", "type": "collection", "format": "collection", "label": "العرض", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "offer.img", "type": "string", "format": "image", "label": "صورة العرض", "description": "صورة العرض", "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "offer.url", "type": "items", "format": "variable-list", "label": "رابط", "value": null, "description": "", "required": false, "icon": "sicon-link", "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}], "multichoice": false, "searchable": true}], "value": [], "minLength": 1, "maxLength": "1"}]}, {"key": "8e372dfe-425b-4c82-8cac-7abdd4397ec0", "title": "عنوان مميز مزخرف", "icon": "sicon-store", "path": "home.specialTitle", "fields": [{"id": "title", "type": "string", "format": "text", "label": "عنوان مميز", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": true, "minLength": "1", "maxLength": 30}]}, {"key": "5255d3d6-cde7-41c2-a81d-accee15a28f8", "title": "احصائيات المتجر", "icon": "sicon-store", "path": "home.storeStats", "fields": [{"id": "description", "type": "static", "format": "description", "label": "وصف العنصر", "value": "قم باختيار ايقونة للاحصائية ثم عنوان ثم رقم للاحصائية", "variant": "h1", "icon": "sicon-format-size"}, {"id": "title", "type": "string", "format": "text", "label": "عنوان المكون", "description": null, "labelHTML": null, "placeholder": "اضف عنوان مثل \"احصائيات المتجر \"", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "5", "maxLength": 30}, {"id": "stats", "type": "collection", "format": "collection", "label": "الاحصائية", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "stats.icon", "type": "string", "format": "icon", "label": "ايقونة", "description": null, "labelHTML": null, "icon": "sicon-user-circle", "value": "sicon-store", "required": false}, {"id": "stats.title", "type": "string", "format": "text", "label": "عنوان الاحصائية", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": 6, "maxLength": 30}, {"id": "stats.number", "type": "number", "format": "integer", "inputType": "number", "label": "رقم الاحصائية", "description": null, "labelHTML": null, "placeholder": "Input an integer number between 5 and 50.", "icon": "sicon-hashtag", "value": null, "required": false, "minimum": 0, "maximum": "10000"}], "value": [], "minLength": 1, "maxLength": "4"}]}, {"key": "6262ac37-e26e-43ba-88ec-9e57e51b636e", "title": "بنرات متحركة جديدة", "icon": "sicon-store", "path": "home.newBanners", "fields": [{"id": "banner", "type": "collection", "format": "collection", "label": "البنر", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "banner.img", "type": "string", "format": "image", "label": "صورة البنر", "description": null, "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "banner.title", "type": "string", "format": "text", "label": "عنوان البنر", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "1", "maxLength": "50"}, {"id": "banner.subtitle", "type": "string", "format": "text", "label": "وصف البنر", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "1", "maxLength": "50"}, {"type": "items", "icon": "sicon-link", "label": "الرابط", "id": "banner.url", "value": null, "description": "", "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}], "value": [], "minLength": 1, "maxLength": "10"}]}, {"key": "a91f248b-374f-4cf5-bd0f-e4b2e32411fc", "title": "ميزة الواتساب", "icon": "sicon-store", "path": "home.whatsapp", "fields": [{"id": "whatsapp", "type": "string", "format": "text", "label": "رقم الواتساب", "description": "ادخل رقم الواتساب مسبوق بمفتاح الدولة", "labelHTML": null, "placeholder": "رقم الها<PERSON><PERSON> علي الواتساب", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "10", "maxLength": "20"}]}, {"key": "ca83d4cf-1735-4976-9595-32220a8df5cd", "title": "معرض مميز", "icon": "sicon-combine_cells", "path": "home.specialGallary", "fields": [{"id": "banners", "type": "collection", "format": "collection", "label": "قائمة الصور", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "banners.image", "type": "string", "format": "image", "label": "صورة البنر", "description": "اختر صورة", "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "banners.title", "type": "string", "format": "text", "label": "عنوان البنر", "description": "قم باضافة عنوان", "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "4", "maxLength": 30}, {"id": "banners.url", "type": "items", "format": "variable-list", "label": "رابط", "value": [], "description": null, "required": false, "icon": "sicon-link", "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}], "multichoice": false, "searchable": true}, {"id": "banners.description", "type": "string", "format": "textarea", "label": "وصف البنر", "description": "قم باضافة نص يعبر عن البنر او الصورة", "labelHTML": null, "placeholder": "Your text here", "icon": "sicon-typography", "value": null, "multilanguage": false, "required": false, "minLength": "5", "maxLength": 255}, {"id": "banners.buttonTitle", "type": "string", "format": "text", "label": "نص ال زر", "description": null, "labelHTML": null, "placeholder": "اضف نص مثلا \"اكتشف الان\"", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": 6, "maxLength": 30}], "value": [], "minLength": 1, "maxLength": "5"}]}, {"key": "b8116e55-9d13-4ccc-adb9-5b54abe5b69a", "title": "بنر فيديو", "icon": "sicon-store", "path": "home.videoBanner", "fields": [{"type": "static", "format": "description", "id": "static-desc", "value": "<div style='padding-top:10px;margin-bottom:30px;border:1px solid #ebebeb;border-radius:5px;text-align:center;overflow:hidden'><h6 style='margin-bottom:10px'>معاينة العنصر</h6><p style='color:#666;font-size:14px;'>بنر فيديو كبير مع عنوان رئيسي وفرعي يظهران على الفيديو</p></div>"}, {"type": "string", "icon": "sicon-video", "label": "رابط الفيديو", "id": "video_url", "value": null, "required": true, "format": "text", "description": "أدخل رابط الفيديو (YouTube, Vimeo, أو رابط مباشر للفيديو)", "placeholder": "https://www.youtube.com/watch?v=VIDEO_ID أو رابط مباشر للفيديو", "minLength": 10, "maxLength": 500}, {"type": "string", "icon": "sicon-format-text-alt", "label": "العنوان الرئيسي", "multilanguage": true, "id": "title", "value": null, "required": false, "format": "text", "description": "العنوان الذي سيظهر على الفيديو بخط كبير وبارز", "placeholder": "أدخل العنوان الرئيسي هنا...", "minLength": 0, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "العنوان الفرعي", "multilanguage": true, "id": "subtitle", "value": null, "description": "النص التوضيحي الذي سيظهر تحت العنوان الرئيسي", "format": "textarea", "required": false, "placeholder": "أدخل العنوان الفرعي أو الوصف هنا...", "minLength": 0, "maxLength": 255}]}], "support_url": "https://example.com", "description": {"ar": "", "en": ""}}