import classCallCheck from '@babel/runtime/helpers/classCallCheck';
import createClass from '@babel/runtime/helpers/createClass';
import possibleConstructorReturn from '@babel/runtime/helpers/possibleConstructorReturn';
import getPrototypeOf from '@babel/runtime/helpers/getPrototypeOf';
import inherits from '@babel/runtime/helpers/inherits';
import wrapNativeSuper from '@babel/runtime/helpers/wrapNativeSuper';

/**
 * مكون بحث مخصص للمنتجات والفئات
 * يقوم بالبحث عن المنتجات والفئات باستخدام Salla Twilight SDK ويعرضها في قائمة منسدلة
 * تم تحسين الأداء وهيكلة الكود للتوافق مع نمط الألعاب (Gaming Theme)
 */
class CategorySearch extends wrapNativeSuper(HTMLElement) {
  constructor() {
    super();
    classCallCheck(this, CategorySearch);
    
    // تعريف المتغيرات الأساسية
    this.searchTimeout = null;
    this.debounceDelay = 300; // تأخير البحث لتحسين الأداء
    this.isOpen = false;
    this.products = [];
    this.categories = [];
    this.showCategories = true;
    this.showProducts = true; // إضافة خاصية للتحكم في ظهور المنتجات
    this.isLoading = false;
    this.processedUrls = new Set(); // تتبع الروابط التي تمت معالجتها لمنع التكرار
    
    // تأخير التهيئة حتى تحميل سلة
    if (window.salla) {
      this.initComponent();
    } else {
      document.addEventListener('salla::ready', () => this.initComponent());
    }
  }

  /**
   * تهيئة المكون
   */
  initComponent() {
    // إنشاء هيكل العناصر
    this._createElements();
    
    // إضافة مستمعي الأحداث
    this._addEventListeners();
      }
      
  /**
   * إنشاء عناصر المكون
   * @private
   */
  _createElements() {
    // إضافة العناصر HTML
    this.innerHTML = `
      <div class="gaming-search-input-container">
        <input 
          type="text" 
          class="gaming-search-input" 
          placeholder="ابحث عن المنتجات والأقسام..." 
          aria-label="بحث"
        />
        <span class="gaming-search-icon">
          <i class="sicon-search"></i>
        </span>
      </div>
      <div class="gaming-search-results">
        <div class="gaming-search-results-content"></div>
        <div class="gaming-search-loading">
          <span class="gaming-search-loading-spinner"></span>
          <span>جاري البحث...</span>
        </div>
        <div class="gaming-search-no-results">
          لا توجد نتائج
        </div>
      </div>
    `;

    // الحصول على العناصر
    this.searchInput = this.querySelector('.gaming-search-input');
    this.searchResults = this.querySelector('.gaming-search-results');
    this.searchResultsContent = this.querySelector('.gaming-search-results-content');
    this.searchLoading = this.querySelector('.gaming-search-loading');
    this.searchNoResults = this.querySelector('.gaming-search-no-results');

    // إخفاء النتائج في البداية
    this.closeResults();
  }
  
  /**
   * إضافة مستمعي الأحداث
   * @private
   */
  _addEventListeners() {
    // البحث عند الكتابة
    this.searchInput.addEventListener('input', this._debounce(this.handleSearch.bind(this), this.debounceDelay));
    
    // عند التركيز على حقل البحث
    this.searchInput.addEventListener('focus', this.handleFocus.bind(this));
    
    // إغلاق النتائج عند النقر خارج المكون
    document.addEventListener('click', this.handleClickOutside.bind(this));
    
    // دعم التنقل بالكيبورد للوصولية
    this.searchInput.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));
}

  /**
   * دالة للحد من عدد مرات تنفيذ الدالة خلال فترة زمنية
   * @param {Function} func - الدالة المراد تنفيذها
   * @param {number} delay - التأخير بالمللي ثانية
   * @returns {Function} - دالة مع تأخير
   * @private
   */
  _debounce(func, delay) {
    return function() {
      const context = this;
      const args = arguments;
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => func.apply(context, args), delay);
    };
  }

  /**
   * معالجة حدث البحث
   */
  handleSearch() {
    const query = this.searchInput.value.trim();
    
    // مسح أي توقيت بحث سابق
    clearTimeout(this.searchTimeout);
    
    // إعادة تعيين الروابط المعالجة
    this.processedUrls.clear();
    
    // إعادة تعيين نتائج البحث السابقة
    this.products = [];
    this.categories = [];
    
    // إذا كان البحث فارغًا، أغلق النتائج
    if (!query) {
      this.closeResults();
      return;
    }

    // عرض النتائج
    this.openResults();
    this.showLoading();
    
    // تأخير البحث لتجنب الطلبات المتكررة
    this.searchTimeout = setTimeout(() => {
      if (this.showProducts) {
        this.searchProducts(query);
      }
      if (this.showCategories) {
        this.searchCategories(query);
      }
    }, this.debounceDelay);
  }
  
  /**
   * معالجة التنقل بالكيبورد
   * @param {KeyboardEvent} event - حدث الكيبورد
   */
  handleKeyboardNavigation(event) {
    if (!this.isOpen) return;
    
    const resultItems = this.searchResultsContent.querySelectorAll('.gaming-search-result-item');
    if (!resultItems.length) return;
    
    let focusedItem = this.searchResultsContent.querySelector('.gaming-search-result-item:focus');
    let index = -1;
    
    if (focusedItem) {
      index = Array.from(resultItems).indexOf(focusedItem);
    }
    
    // التنقل لأسفل (السهم لأسفل أو Tab)
    if (event.key === 'ArrowDown' || (event.key === 'Tab' && !event.shiftKey)) {
      event.preventDefault();
      index = (index + 1) % resultItems.length;
      resultItems[index].focus();
    }
    
    // التنقل لأعلى (السهم لأعلى أو Shift+Tab)
    else if (event.key === 'ArrowUp' || (event.key === 'Tab' && event.shiftKey)) {
      event.preventDefault();
      index = (index - 1 + resultItems.length) % resultItems.length;
      resultItems[index].focus();
    }
    
    // إغلاق النتائج (Escape)
    else if (event.key === 'Escape') {
      this.closeResults();
      this.searchInput.focus();
    }
  }

  /**
   * البحث عن المنتجات
   * @param {string} query - نص البحث
   */
  searchProducts(query) {
    this.isLoading = true;
    this.showLoading();
    
    // البحث المحلي أولاً
    this.localSearchProducts(query);
    
    // ثم محاولة البحث باستخدام SDK
    try {
      if (window.salla && window.salla.product) {
        window.salla.product.fetch({
          search: query,
          limit: 10
        })
        .then(response => {
          if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
            // إضافة درجة الصلة للمنتجات
            const productsWithRelevance = response.data.map(product => {
              product.relevance = this._calculateRelevance(product.name, query);
              return product;
            });
            
            // تصفية المنتجات المكررة
            const filteredProducts = productsWithRelevance.filter(product => 
              !this.processedUrls.has(product.url)
            );
            
            // إضافة الروابط المعالجة
            filteredProducts.forEach(product => {
              this.processedUrls.add(product.url);
            });
            
            // دمج النتائج
            this.products = this._mergeResults(this.products, filteredProducts);
            
            // تحديث العرض
            this.renderResults();
          }
        })
        .catch(() => {
          // عند حدوث خطأ، نعتمد على النتائج المحلية فقط
          this.renderResults();
        });
      }
    } catch (error) {
      this.renderResults();
    }
  }

  /**
   * البحث عن الفئات
   * @param {string} query - نص البحث
   */
  searchCategories(query) {
    // البحث المحلي أولاً
    this.localSearchCategories(query);
    
    try {
      if (window.salla && window.salla.category) {
        window.salla.category.fetch({
          search: query,
          limit: 10
        })
        .then(response => {
          if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
            // إضافة درجة الصلة للفئات
            const categoriesWithRelevance = response.data
              .filter(cat => cat.status === 'active')
              .map(category => {
                category.relevance = this._calculateRelevance(category.name, query);
                return category;
              });
            
            // تصفية الفئات المكررة
            const filteredCategories = categoriesWithRelevance.filter(category => 
              !this.processedUrls.has(category.url)
            );
            
            // إضافة الروابط المعالجة
            filteredCategories.forEach(category => {
              this.processedUrls.add(category.url);
            });
            
            // دمج النتائج
            this.categories = this._mergeResults(this.categories, filteredCategories);
            
            // تحديث العرض
            this.renderResults();
          }
        })
        .catch(() => {
          // عند حدوث خطأ، نعتمد على النتائج المحلية فقط
          this.renderResults();
        });
      }
    } catch (error) {
      this.renderResults();
    }
  }

  /**
   * حساب درجة صلة النص بالبحث
   * @param {string} text - النص المراد فحصه
   * @param {string} query - نص البحث
   * @returns {number} - درجة الصلة من 0 إلى 100
   * @private
   */
  _calculateRelevance(text, query) {
    if (!text || !query) return 0;
    
    const textLower = text.toLowerCase();
    const queryLower = query.toLowerCase();
    let relevanceScore = 0;
    
    // تطابق النص الكامل
    if (textLower.includes(queryLower)) {
      relevanceScore += 60;
      
      // وزن إضافي للكلمات التي تبدأ بالاستعلام
      if (textLower.startsWith(queryLower)) {
        relevanceScore += 30;
    }
  }
    
    // تطابق الكلمات الفردية
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
    let matchedWords = 0;
    
    queryWords.forEach(word => {
      if (textLower.includes(word)) {
        matchedWords++;
      }
    });
    
    if (matchedWords > 0 && queryWords.length > 0) {
      relevanceScore += (matchedWords / queryWords.length) * 40;
    }
    
    return relevanceScore;
  }

  /**
   * دمج نتائج البحث مع إزالة العناصر المتكررة
   * @param {Array} existingResults - النتائج الحالية
   * @param {Array} newResults - النتائج الجديدة
   * @returns {Array} - النتائج المدمجة بدون تكرار
   * @private
   */
  _mergeResults(existingResults, newResults) {
    // إنشاء نسخة من النتائج الحالية
    const mergedResults = [...existingResults];
    
    // إنشاء مصفوفة من الروابط الموجودة حاليا
    const existingUrls = existingResults.map(item => item.url);
    
    // إضافة النتائج الجديدة التي لا توجد في النتائج الحالية
    newResults.forEach(newItem => {
      // إذا كان العنصر الجديد غير موجود في النتائج الحالية، أضفه
      if (!existingUrls.includes(newItem.url) && newItem.name) {
        mergedResults.push(newItem);
      }
    });
    
    // ترتيب النتائج حسب درجة الصلة
    return mergedResults.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * البحث المحلي عن المنتجات
   * @param {string} query - نص البحث
   */
  localSearchProducts(query) {
    // تحسين اختيار عناصر المنتجات لتغطية جميع أنواع المنتجات المحتملة
    const productElements = document.querySelectorAll('.product-entry, .product-card, [data-product-id], .product, .product-block, [data-type="product"], .card--product, .s-product-card, .product-item, [id*="product"], [class*="product"]');
    const results = [];
    
    query = query.toLowerCase();
    const queryWords = query.split(/\s+/).filter(word => word.length > 2);
    
    // ابحث أولاً في العناصر المتوفرة
    this._searchInAvailableProducts(productElements, query, queryWords, results);
    
    // إذا لم نجد منتجات كافية، ابحث عن أي روابط تحتوي على كلمة "product" أو "منتج"
    if (results.length < 2) {
      this._searchInAllLinks(query, queryWords, results);
    }
    
    // ترتيب النتائج حسب الصلة
    results.sort((a, b) => b.relevance - a.relevance);
    
    if (results.length > 0) {
      this.products = results;
      this.renderResults();
    }
  }

  /**
   * البحث في عناصر المنتجات المتوفرة في الصفحة
   * @param {NodeList} productElements - عناصر المنتجات
   * @param {string} query - نص البحث
   * @param {Array} queryWords - كلمات البحث
   * @param {Array} results - مصفوفة النتائج
   * @private
   */
  _searchInAvailableProducts(productElements, query, queryWords, results) {
    productElements.forEach(element => {
      try {
        // تحسين اختيار اسم المنتج
        const productName = element.querySelector('.product-title, .product-name, h3, a, .name, .title, [itemprop="name"]')?.textContent?.trim() || element.textContent?.trim() || '';
        
        // الحصول على الرابط بطرق مختلفة
        let productLink = null;
        const linkElement = element.querySelector('a[href]');
        if (linkElement) {
          productLink = linkElement.href;
        } else if (element.tagName === 'A') {
          productLink = element.href;
        } else if (element.dataset.url) {
          productLink = element.dataset.url;
        } else if (element.dataset.href) {
          productLink = element.dataset.href;
        } else if (element.onclick) {
          // محاولة استخراج الرابط من وظيفة النقر
          const onClickStr = element.onclick.toString();
          const urlMatch = onClickStr.match(/(window\.location|location\.href)\s*=\s*['"]([^'"]+)['"]/);
          if (urlMatch && urlMatch[2]) {
            productLink = urlMatch[2];
          }
        }
        
        // إذا لم نجد رابطًا، تخطي هذا العنصر
        if (!productLink) return;
        
        // تخطي المنتجات المكررة
        if (this.processedUrls.has(productLink)) return;
        
        // تحسين اختيار صورة المنتج
        let productImage = '';
        const imgElement = element.querySelector('img');
        if (imgElement) {
          productImage = imgElement.src;
        } else {
          // محاولة البحث عن الصورة في الأنماط الخلفية
          const computedStyle = window.getComputedStyle(element);
          const backgroundImage = computedStyle.backgroundImage;
          if (backgroundImage && backgroundImage !== 'none') {
            const urlMatch = backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
            if (urlMatch && urlMatch[1]) {
              productImage = urlMatch[1];
            }
          }
        }
        
        // تحسين اختيار سعر المنتج
        const productPrice = element.querySelector('.product-price, .price, .s-price, [itemprop="price"], .price-wrapper')?.textContent || '';
        
        // حساب درجة الصلة
        let relevanceScore = this._calculateRelevance(productName, query);
        
        // التحقق من تطابق المنتج مع البحث
        if (relevanceScore === 0 && 
            !productName.toLowerCase().includes(query.toLowerCase()) && 
            !this._anyWordMatch(productName, queryWords)) {
          
          // قبل التخطي، نتحقق من وجود مطابقة في الرابط نفسه
          const linkMatch = productLink.toLowerCase().includes(query.toLowerCase()) || 
                           queryWords.some(word => productLink.toLowerCase().includes(word));
          
          if (!linkMatch) return;
          // إذا كانت هناك مطابقة في الرابط، نعطي درجة صلة منخفضة
          relevanceScore = 5;
        }
        
        // إضافة الرابط إلى المجموعة
        this.processedUrls.add(productLink);
        
        // تخزين النتيجة مع البيانات
        results.push({
          name: productName,
          url: productLink,
          image: productImage,
          thumbnail: productImage,
          price: productPrice,
          relevance: relevanceScore > 0 ? relevanceScore : 10, // ضمان وجود قيمة صلة موجبة
          type: 'product' // إضافة النوع للتمييز
        });
      } catch (error) {
        // تجاهل الأخطاء في عناصر فردية
        console.error('Error processing product element:', error);
      }
    });
  }

  /**
   * البحث في جميع الروابط في الصفحة
   * @param {string} query - نص البحث
   * @param {Array} queryWords - كلمات البحث
   * @param {Array} results - مصفوفة النتائج
   * @private
   */
  _searchInAllLinks(query, queryWords, results) {
    // البحث في جميع الروابط التي قد تكون منتجات
    const allLinks = document.querySelectorAll('a[href]');
    
    allLinks.forEach(link => {
      try {
        // تخطي الروابط المعالجة بالفعل
        if (this.processedUrls.has(link.href)) return;
        
        const linkUrl = link.href.toLowerCase();
        const linkText = link.textContent.trim();
        
        // التحقق مما إذا كان الرابط يشير إلى منتج
        const isProductLink = linkUrl.includes('/product/') || 
                              linkUrl.includes('/products/') || 
                              linkUrl.includes('product_id=') ||
                              linkUrl.includes('منتج') ||
                              linkUrl.includes('بطاقة');
        
        // إذا كان الرابط ليس منتجًا، تخطيه إلا إذا كان النص يتطابق مع البحث
        if (!isProductLink && 
            !linkText.toLowerCase().includes(query) && 
            !this._anyWordMatch(linkText, queryWords)) {
          return;
        }
        
        // حساب درجة الصلة
        const relevanceScore = this._calculateRelevance(linkText, query);
        
        // الحصول على الصورة من النص أو الصورة داخل الرابط
        let productImage = '';
        const imgElement = link.querySelector('img');
        if (imgElement) {
          productImage = imgElement.src;
        }
        
        // إضافة الرابط إلى المجموعة
        this.processedUrls.add(link.href);
        
        // تخزين النتيجة
        results.push({
          name: linkText,
          url: link.href,
          image: productImage,
          thumbnail: productImage,
          relevance: relevanceScore > 0 ? relevanceScore : (isProductLink ? 15 : 5),
          type: 'product'
        });
        
      } catch (error) {
        // تجاهل الأخطاء
        console.error('Error processing link:', error);
      }
    });
  }

  /**
   * التحقق من تطابق أي كلمة في النص مع الكلمات في البحث
   * @param {string} text - النص المراد فحصه
   * @param {Array} queryWords - كلمات البحث
   * @returns {boolean} - يعيد true إذا كان هناك أي تطابق
   * @private
   */
  _anyWordMatch(text, queryWords) {
    if (!text || !queryWords || queryWords.length === 0) return false;
    text = text.toLowerCase();
    return queryWords.some(word => text.includes(word));
  }

  /**
   * البحث المحلي عن الفئات
   * @param {string} query - نص البحث
   */
  localSearchCategories(query) {
    const categoryElements = document.querySelectorAll('.category-entry, .category-card, [data-category-id], .nav-link, .categories-menu a, .main-menu a');
    const results = [];
    
    query = query.toLowerCase();
    const queryWords = query.split(/\s+/).filter(word => word.length > 2);
    
    categoryElements.forEach(element => {
      try {
        const categoryName = element.textContent.trim();
        const categoryLink = element.href || '#';
        
        // تخطي الفئات المكررة
        if (this.processedUrls.has(categoryLink)) return;
        
        const categoryImage = element.querySelector('img')?.src || '';
        
        // حساب درجة الصلة
        const relevanceScore = this._calculateRelevance(categoryName, query);
        
        // إذا كانت النتيجة تساوي صفر، نتخطى هذه الفئة
        if (relevanceScore === 0) {
          return;
        }
        
        // إضافة الرابط إلى المجموعة
        this.processedUrls.add(categoryLink);
        
        // تخزين النتيجة مع البيانات
        results.push({
          name: categoryName,
          url: categoryLink,
          image: categoryImage,
          thumbnail: categoryImage,
          relevance: relevanceScore,
          type: 'category' // إضافة النوع للتمييز
        });
      } catch (error) {
        // تجاهل الأخطاء في عناصر فردية
      }
    });
    
    // ترتيب النتائج حسب الصلة
    results.sort((a, b) => b.relevance - a.relevance);
    
    if (results.length > 0) {
      this.categories = results;
      this.renderResults();
    }
  }

  /**
   * عرض نتائج البحث
   */
  renderResults() {
    this.hideLoading();
    
    const hasProducts = this.products.length > 0;
    const hasCategories = this.categories.length > 0 && this.showCategories;
    
    // إذا لم تكن هناك نتائج، أظهر رسالة عدم وجود نتائج
    if (!hasProducts && !hasCategories) {
      this.showNoResults();
      return;
    }
    
    // إخفاء رسالة عدم وجود نتائج
    this.hideNoResults();
    
    // إنشاء عناصر النتائج
    this.searchResultsContent.innerHTML = '';
    
    // عرض الفئات أولاً
    if (hasCategories) {
      this._renderCategoriesSection();
    }
    
    // عرض المنتجات
    if (hasProducts) {
      this._renderProductsSection();
    }
  }
  
  /**
   * عرض قسم الفئات
   * @private
   */
  _renderCategoriesSection() {
      const categoriesHeader = document.createElement('h3');
    categoriesHeader.className = 'gaming-search-section-title';
      categoriesHeader.textContent = 'الأقسام';
      this.searchResultsContent.appendChild(categoriesHeader);
      
      // تحديد الحد الأقصى للفئات المعروضة
      const maxCategories = Math.min(this.categories.length, 5);
      
      for (let i = 0; i < maxCategories; i++) {
        const category = this.categories[i];
      const resultItem = this._createResultItem(category, 'category');
        this.searchResultsContent.appendChild(resultItem);
      }
    }
    
  /**
   * عرض قسم المنتجات
   * @private
   */
  _renderProductsSection() {
      const productsHeader = document.createElement('h3');
    productsHeader.className = 'gaming-search-section-title';
      productsHeader.textContent = 'المنتجات';
      this.searchResultsContent.appendChild(productsHeader);
      
      // تحديد الحد الأقصى للمنتجات المعروضة
      const maxProducts = Math.min(this.products.length, 5);
      
      for (let i = 0; i < maxProducts; i++) {
        const product = this.products[i];
      const resultItem = this._createResultItem(product, 'product');
      this.searchResultsContent.appendChild(resultItem);
    }
  }
  
  /**
   * إنشاء عنصر نتيجة
   * @param {Object} item - عنصر النتيجة (منتج أو فئة)
   * @param {string} type - نوع العنصر ('product' أو 'category')
   * @returns {HTMLElement} - عنصر HTML
   * @private
   */
  _createResultItem(item, type) {
    const resultItem = document.createElement('a');
    
    // التأكد من أن الرابط صالح
    let url = item.url || '#';
    
    // إضافة اسم المجال إذا كان الرابط نسبيًا ولا يبدأ بـ "/"
    if (url.startsWith('/')) {
      // إذا كان الرابط نسبيًا يبدأ بـ "/" أضف المجال
      url = window.location.origin + url;
    } else if (!url.startsWith('http') && !url.startsWith('#')) {
      // إذا كان الرابط لا يبدأ بـ "http" أو "#"، أضف "/" والمجال
      url = window.location.origin + '/' + url;
    }
    
    resultItem.href = url;
    resultItem.className = 'gaming-search-result-item';
    resultItem.setAttribute('tabindex', '0');
    resultItem.setAttribute('role', 'option');
    
    // إضافة onclick لجعل النقر يعمل بشكل مؤكد
    resultItem.onclick = function(e) {
      if (url !== '#') {
        // حفظ الرابط في التاريخ
        window.location.href = url;
      }
    };
    
    // إضافة مؤشر الصلة بالبحث
    if (item.relevance && item.relevance > 90) {
      resultItem.classList.add('high-relevance');
    }
    
    // صورة العنصر
    let imageHtml = '';
    if (item.thumbnail || item.image) {
      const imageSrc = item.thumbnail || item.image;
      imageHtml = `<img src="${imageSrc}" alt="${item.name}" class="gaming-search-result-image" loading="lazy" onerror="this.src='data:image/svg+xml;utf8,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2240%22 height=%2240%22 viewBox=%220 0 24 24%22><path fill=%22%231DE9B6%22 d=%22M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4-8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4z%22/></svg>'"/>`;
    } else {
      const iconClass = type === 'product' ? 'sicon-box' : 'sicon-folder';
      imageHtml = `<div class="gaming-search-result-icon">
        <i class="${iconClass}"></i>
      </div>`;
    }
    
    // سعر المنتج (للمنتجات فقط)
    let priceHtml = '';
    if (type === 'product' && (item.sale_price || item.price)) {
      let priceText = item.price || '';
      let saleText = '';
      
      if (item.sale_price && item.regular_price) {
        priceText = item.sale_price;
        saleText = item.regular_price;
      }
      
      priceHtml = `
        <div class="gaming-search-result-price">
          <span class="gaming-search-result-current-price">${priceText}</span>
          ${saleText ? `<span class="gaming-search-result-old-price">${saleText}</span>` : ''}
        </div>
      `;
    }
    
    resultItem.innerHTML = `
      <div class="gaming-search-result-wrapper">
        ${imageHtml}
        <div class="gaming-search-result-content">
          <div class="gaming-search-result-name">${item.name}</div>
          ${priceHtml}
        </div>
      </div>
    `;
    
    return resultItem;
  }

  /**
   * فتح نتائج البحث
   */
  openResults() {
    if (!this.isOpen) {
      this.searchResults.classList.add('gaming-search-results-open');
      this.isOpen = true;
    }
  }

  /**
   * إغلاق نتائج البحث
   */
  closeResults() {
    if (this.isOpen) {
      this.searchResults.classList.remove('gaming-search-results-open');
      this.isOpen = false;
    }
  }

  /**
   * معالجة النقر خارج المكون
   * @param {Event} event - حدث النقر
   */
  handleClickOutside(event) {
    if (!this.contains(event.target)) {
      this.closeResults();
    }
  }

  /**
   * معالجة التركيز على حقل البحث
   */
  handleFocus() {
    const query = this.searchInput.value.trim();
    if (query) {
      if (this.products.length || this.categories.length) {
        this.openResults();
      } else {
        this.searchProducts(query);
        if (this.showCategories) {
        this.searchCategories(query);
      }
    }
  }
  }

  /**
   * إظهار مؤشر التحميل
   */
  showLoading() {
    this.searchLoading.classList.add('gaming-search-loading-visible');
    this.searchNoResults.classList.remove('gaming-search-no-results-visible');
  }

  /**
   * إخفاء مؤشر التحميل
   */
  hideLoading() {
    this.searchLoading.classList.remove('gaming-search-loading-visible');
    this.isLoading = false;
  }

  /**
   * إظهار رسالة عدم وجود نتائج
   */
  showNoResults() {
    this.searchNoResults.classList.add('gaming-search-no-results-visible');
  }

  /**
   * إخفاء رسالة عدم وجود نتائج
   */
  hideNoResults() {
    this.searchNoResults.classList.remove('gaming-search-no-results-visible');
  }
}

// تسجيل المكون المخصص
customElements.define('category-search', CategorySearch);

export default CategorySearch; 