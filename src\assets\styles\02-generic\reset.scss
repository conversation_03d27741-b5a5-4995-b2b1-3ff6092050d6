body {
  font-size: 15px;
  font-weight: 400;
  line-height: 26px;
  color: var(--main-text-color);
  position: relative;
}

// p {
//   font-size: 16px;
// }

html {
  width: 100%;
  height: 100%;
  position: relative;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  overflow-x: hidden; /* Prevent horizontal scrolling */

  /* Hide scrollbar but allow scrolling */
  &::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari and Opera */
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

body {
  border: 0;
  margin: 0;
  padding: 0;
}

h1, .h1,
h2, .h2,
h3, .h3,
h4, .h4,
h5, .h5,
h6, .h6 {
  font-weight: 600;
  margin-bottom: 0px;
}

a {
  text-decoration: none;
  transition: color 0.2s;
}

img {
  max-width: 100%;
}

a:focus {
  outline: none;
  text-decoration: none;
}

ul,
li {
  margin: 0;
  padding: 0;
  list-style: none;
}


h1 {
  @apply text-3xl;
}

h2 {
  @apply text-xl;
}

h3 {
  @apply text-xl;
  line-height: 38px;
}

// h4 {
//   font-size: 22px;
//   line-height: 32px;
// }

// h5 {
//   font-size: 16px;
//   line-height: 24px;
// }

// h6 {
//   font-size: 14px;
//   line-height: 26px;
// }

.f-color {
  color: #FF6767;
}

h5.subtitle {
  font-size: 16px;
  font-weight: 400;
}