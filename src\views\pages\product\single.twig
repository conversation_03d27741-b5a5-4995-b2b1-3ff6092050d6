{#
| Variable                               | Type                        | Description                                                            |
|----------------------------------------|-----------------------------|------------------------------------------------------------------------|
| page                                   | object                      |                                                                        |
| page.title                             | string                      | *could be html                                                         |
| page.slug                              | string                      | ex: "cat.show"                                                         |
| product                                | Product                     |                                                                        |
| product.id                             | int                         |                                                                        |
| product.name                           | string                      |                                                                        |
| product.description                    | string                      | HTML                                                                   |
| product.url                            | string                      |                                                                        |
| product.promotion_title                | string                      |                                                                        |
| product.subtitle                       | string                      |                                                                        |
| product.type                           | string                      | product, service, group_products, codes, digital, food, donating       |
| product.status                         | string                      | sale, out, out-and-notify                                              |
| product.weight                         | ?string                     |                                                                        |
| product.calories                       | ?float                      |                                                                        |
| product.sku                            | ?string                     |                                                                        |
| product.rating                         | ?Rating                     |                                                                        |
| product.rating.count                   | int                         |                                                                        |
| product.rating.stars                   | int                         |                                                                        |
| product.price                          | float                       | can be string too `-`, when merchant doesn't want to show zero         |
| product.sale_price                     | float                       |                                                                        |
| product.regular_price                  | float                       |                                                                        |
| product.starting_price                 | ?float                      |                                                                        |
| product.quantity                       | ?int                        | if it's null, means it's unlimited                                     |
| product.sold_quantity                  | int                         |                                                                        |
| product.max_quantity                   | int                         |                                                                        |
| product.discount_ends                  | ?date                       |                                                                        |
| product.is_taxable                     | bool                        | Is the tax included in the price                                       |
| product.category                       | ?Category                   |                                                                        |
| product.category.name                  | string                      |                                                                        |
| product.category.url                   | string                      |                                                                        |
| product.image                          | object                      |                                                                        |
| product.image.url                      | string                      |                                                                        |
| product.image.alt                      | ?string                     |                                                                        |
| product.images                         | array                       |                                                                        |
| product.images[].id                    | int                         |                                                                        |
| product.images[].url                   | string                      |                                                                        |
| product.images[].alt                   | ?string                     |                                                                        |
| product.images[].video_url             | ?string                     |                                                                        |
| product.images[].type                  | string                      | `image` `video`                                                        |
| product.brand                          | ?object                     |                                                                        |
| product.brand.id                       | int                         |                                                                        |
| product.brand.url                      | ?string                     |                                                                        |
| product.brand.name                     | ?string                     |                                                                        |
| product.brand.logo                     | ?string                     |                                                                        |
| product.tags                           | ?Tags[] *Collection         |                                                                        |
| product.tags[].name                    | string                      |                                                                        |
| product.tags[].url                     | string                      |                                                                        |
| product.options                        | ProductOption[] *Collection | @see views/pages/partials/product/options.twig                         |
| product.notify_availability            | ?object                     | does product outOfStock & can visitor subscribe to notify availability |
| product.notify_availability.channels   | array                       | ex: ['sms', 'email']                                                   |
| product.notify_availability.subscribed | bool                        | Does user subscribed before                                            |
| product.donation                       | ?ProductDonation            |                                                                        |
| product.donation.target_message        | ?string                     | Message if reached to target or target expired                         |
| product.donation.collected_amount      | float                       |                                                                        |
| product.donation.target_amount         | float                       |                                                                        |
| product.donation.target_percent        | float                       |                                                                        |
| product.donation.target_end_date       | ?Date                       |                                                                        |
| product.donation.can_donate            | bool                        | `true` When there is no campaign or (target not reached & not expired) |
| product.has_read_more                  | bool                        |                                                                        |
| product.can_add_note                   | bool                        |                                                                        |
| product.can_show_remained_quantity     | bool                        |                                                                        |
| product.can_show_sold                  | bool                        |                                                                        |
| product.can_upload_file                | bool                        |                                                                        |
| product.has_custom_form                | bool                        | Is it for  `Food` or `Custom Service` product                          |
| product.has_metadata                   | bool                        |                                                                        |
| product.has_options                    | bool                        |                                                                        |
| product.is_on_sale                     | bool                        | Product has discounted price                                           |
| product.is_hidden_quantity             | bool                        | The quantity is hidden by merchant, or product not available           |
| product.is_available                   | bool                        |                                                                        |
| product.is_in_wishlist                 | bool                        |                                                                        |
| product.is_out_of_stock                | bool                        |                                                                        |
| product.is_require_shipping            | bool                        |                                                                        |
| product.base_currency_price            | float                       | product.price with base currency (SAR)                                 |
| product.discount_percentage            | ?string                     | ex: "20%"                                                              |
| product.has_3d_image                   | bool                        |                                                                        |
| product.has_size_guide                 | bool                        |                                                                        |
| product.is_giftable                    | bool                        |                                                                        |
| product.add_to_cart_label              | string                      |                                                                        |
| product.currency                       | string                      |                                                                        |
#}
{% extends "layouts.master" %}
{% block content %}
    <div class="container gaming-product-detail">
        {# add breadcumbs container in pages to make a space in case breadcrumbs is off #}
        <nav class="breadcrumbs w-full py-5">
            <salla-breadcrumb></salla-breadcrumb>
        </nav>
        <div class="flex flex-col items-start md:flex-row" id="product-{{ product.id }}">
            <div class="sidebar md:sticky top-24 w-full md:!w-2/4 rtl:ml-8 ltr:mr-8 pb-8 md:pb-16 overflow-hidden shrink-0">
                {% set has_many_images = product.images|length > 1 %}
                <salla-slider
                        id="details-slider-{{ product.id }}"
                        class="details-slider rounded-md image-slider"
                        type="thumbs"
                        loop="false"
                        auto-height
                        listen-to-thumbnails-option
                        show-thumbs-controls="false">
                    {% if product.promotion_title %}
                        <div class="promotion-title">{{ product.promotion_title }}</div>
                    {% endif %}

                    <salla-button
                            class="btn--wishlist animated sws"
                            data-id="{{ product.id }}"
                            onclick="salla.wishlist.toggle({{ product.id }})"
                            shape="icon"
                            fill="outline"
                            color="light">
                        <i class="sicon-heart"></i>
                    </salla-button>

                    {# Calories Badge #}
                    {% if product.calories %}
                        <div class="absolute z-[2] top-4 rtl:left-4 ltr:right-4 bg-white shadow-sm flex flex-col justify-center items-center rounded-full w-20 h-20 md:w-24 md:h-24">
                            <span class="text-red-500 text-xl leading-none font-bold">{{ product.calories }}</span>
                            <span class="text-xs text-gray-500">{{ trans('pages.products.calories') }}</span>
                        </div>
                    {% endif %}

                    <div slot="items">
                        {% for image in product.images %}
                            {% if image.three_d_image_url %}
                                <model-viewer style="min-height: 500px;" class="swiper-slide model-entry w-full h-full"
                                    loading="eager" camera-controls
                                    touch-action="pan-y" auto-rotate
                                    poster="{{ image.url }}"
                                    src="{{ image.three_d_image_url }}"
                                    shadow-intensity="1" alt="{{ image.alt }}">
                                </model-viewer>
                            {% else %}
                                <a data-fslightbox="product_{{ product.id }}"
                                   data-img-id="{{ image.id }}"
                                   data-slid-index="{{ loop.index-1 }}"{% if image.video_url %} data-video-src="{{ image.video_url }}"{% endif %}
                                   data-caption="{{ image.alt }}"
                                   data-infinite="false"
                                   data-type="{{ image.video_url?'youtube':'image' }}"
                                   href="{{ (image.video_url is iterable ? image.video_url|first : image.video_url)|default(image.url) }}"
                                   aria-label="{{ product.name }}"
                                   class="swiper-slide magnify-wrapper homeslider__slide {{ image.video_url?'video-entry':'' }}">
                                    {% if loop.first %}
                                        <img id="{{image.id}}" fetchpriority="high" loading="eager" src="{{ image.url }}"
                                             alt="{{ image.alt }}"
                                             class="h-full object-{{ theme.settings.get('slider_background_size') }} w-full {{ image.three_d_image_url ? 'model-poster' : '' }}">
                                    {% else %}
                                        <img id="{{image.id}}" src="{{ 'images/s-empty.png' | cdn }}" data-src="{{ image.url }}"
                                             alt="{{ image.alt }}"
                                             class="lazy h-full object-{{ theme.settings.get('slider_background_size') }} w-full {{ image.three_d_image_url ? 'model-poster' : '' }}">
                                    {% endif %}
                                </a>
                            {% endif %}
                        {% endfor %}
                    </div>

                    {% if has_many_images %}
                        <div slot="thumbs">
                            {% for image in product.images %}
                                <div class="slide--one-fourth {{ image.video_url?'video-entry':'' }} {{ image.three_d_image_url?'model-entry':'' }}">
                                    {% if loop.index < 5 %}
                                        <img src="{{ image.url }}"
                                             loading="eager"
                                             class="object-cover w-full h-full bg-gray-100 rounded-md overflow-hidden"
                                             title="{{ image.alt }}"
                                             alt="{{ image.alt }}"/>
                                    {% else %}
                                        <img data-src="{{ image.url }}" src="{{ 'images/s-empty.png' | cdn }}"
                                             class="object-cover w-full h-full lazy bg-gray-100 rounded-md overflow-hidden"
                                             title="{{ image.alt }}"
                                             alt="{{ image.alt }}"/>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </salla-slider>
            </div>

            <div class="main-content md:sticky md:overflow-hidden top-24 w-full md:w-2/4 md:pb-16 product-info">

                {% hook 'product:single.description.start' %}

                {% if product.brand.name %}
                    <div class="product-brand mb-5 w-12">
                        <a class="brand-logo" href="{{ product.brand.url }}" title="{{ product.brand.name }}">
                            <img width="100%" height="100%" class="max-h-full object-contain"
                                 src="{{ product.brand.logo }}"
                                 title="{{ product.brand.name }}"
                                 alt="{{ product.brand.name }}">
                        </a>
                    </div>
                {% endif %}

                <h1 class="text-xl md:text-2xl leading-10 font-bold mb-6 text-gray-800 product-title">{{ product.name }}</h1>

                {% if product.subtitle %}
                    <h2 class="product-entry__sub-title text-sm text-gray-500 leading-6 mb-2.5">
                        {{ product.subtitle }}
                    </h2>
                {% endif %}

                {% if product.rating %}
                    <salla-rating-stars value="{{ product.rating.stars }}"
                                        reviews="{{ product.rating.count }}"></salla-rating-stars>
                {% endif %}

                {% if product.is_taxable %}
                    <small class="color-grey">{{ trans('pages.products.tax_included') }}</small>
                {% endif %}
                {# Price #}
                <div class="flex whitespace-nowrap gap-4 items-center product-price">
                  <div class="{{ product.is_on_sale ? '' : 'hidden' }} space-x-2 rtl:space-x-reverse whitespace-nowrap">
                      <h4 class="text-red-800 font-bold text-xl inline-block total-price">{{product.sale_price|money}}</h4>
                      <span class="text-gray-500 line-through before-price">{{product.regular_price|money}}</span>
                  </div>
                  <div class="gap-4 {{ product.is_on_sale ? 'hidden' : 'flex' }}">
                      {% if product.starting_price %}
                          <span class="">{{ trans('pages.products.starting_price') }}</span>
                      {% endif %}
                          <h2 class="font-bold text-xl inline-block total-price"> {{product.starting_price ? product.starting_price|money : product.price|money }}</h2>
                  </div>
                </div>

                <div class="product__description p-2 px-5 sm:p-1 leading-7 mb-3 product-description">
                    {% set product_desc = product.description|replace({"&nbsp;":"\n"}) %}
                    {% if product.has_read_more %}
                        <article class="article article--main relative leading-8 overflow-hidden transition-all max-h-0 duration-300 py-4"
                            id="more-content" style="max-height:5.25rem">
                            {{ product_desc|raw }}
                        </article>

                        {# Read More Button #}
                        <a id="btn-show-more" class="link--primary inline-block mt-2 cursor-pointer">
                            {{ trans('pages.products.read_more') }}
                        </a>
                    {% else %}
                        <article class="article--main pb-1">
                            {{ product_desc|raw }}
                        </article>
                    {% endif %}
                </div>

                {% if product.tags|length and theme.settings.get('show_tags', true) %}
                    <div class="mb-3">
                        {% for tag in product.tags %}
                            <a href="{{ tag.url }}"
                               class="rtl:ml-2 ltr:mr-2 inline-flex  text-gray-500 hover:text-primary underline text-sm mb-1">{{ tag.name }}
                                ,</a>
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="flex rtl:space-x-reverse space-x-3">
                    <salla-social-share class="inline-flex mb-5" aria-label="social share"></salla-social-share>

                    <salla-button
                            class="btn--wishlist animated hidden sm:inline-flex"
                            data-id="{{ product.id }}"
                            onclick="salla.wishlist.toggle({{ product.id }})"
                            shape="icon"
                            fill="outline"
                            color="light"
							aria-label="add to wishlist">
                        <i class="sicon-heart"></i>
                    </salla-button>
                </div>

                {% if product.sold_quantity or product.can_show_remained_quantity %}
                    <div class="bg-white py-2.5 mb-5 rounded-md inline-flex text-sm">
                        {% if product.sold_quantity %}
                            <div class="px-4 !text-red-800">
                                <i class="sicon-fire rtl:ml-1.5 ltr:mr-1.5"></i> {{ trans('pages.products.sold') }}
                                <span>{{ pluralize('pages.products.sold_times', product.sold_quantity)|raw }}</span>
                            </div>
                        {% endif %}
                        {% if product.can_show_remained_quantity %}
                            <div class="px-4 rtl:even:border-r ltr:even:border-l even:border-gray-200">
                                <i class="!text-red-800 sicon-box-bankers rtl:ml-1.5 ltr:mr-1.5"></i>
                                {{ trans('pages.products.remained') }}
                                <span>{{ product.quantity }}</span>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
                {% if product.sku %}
                    <div class="mb-4 flex justify-between sm:grid sm:grid-cols-3 bg-white rounded-md px-4 py-2">
                        <div class="flex items-center">
                            <i class="sicon-barcode mx-1"></i>
                            {{ trans('pages.products.sku') }}
                        </div>
                        <div class="text-unicode">{{ product.sku }}</div>
                    </div>
                {% endif %}
                {% hook 'product:single.description' %}

                {# Installment Methods #}
                <salla-installment price="{{ product.price }}"></salla-installment>
                {% if product.has_metadata %}
                <salla-metadata></salla-metadata>
                {% endif %}

                {% if product.show_availability %}
                    <section class="bg-white p-5 rounded-md mb-5 last:mb-0">
                        <div class="center-between">
                            <label class="flex items-center text-base">
                                <span class="sicon-location rtl:ml-1 ltr:mr-1"></span>
                                <span class="inline-block">{{ trans('pages.products.availability') }}</span>
                            </label>
                            <div class="mt-1 sm:mt-0 sm:col-span-2 text-end">
                                <div class="flex rtl:space-x-reverse space-x-3">
                                    <a href="#!"
                                       onclick="salla.event.dispatch('scopes::open', {mode: 'availability', product_id: {{ product.id }} })"
                                       class="group text-primary flex items-center justify-center">
                                        <span>{{ trans('pages.products.select_branch') }}</span>
                                        <span class="sicon-keyboard_arrow_left mr-2 transition-all group-hover:-translate-x-1"></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>
                {% endif %}

                <form class="form product-form" enctype="multipart/form-data" method="post"
                      onsubmit="return salla.form.onSubmit('cart.addItem', event)" >
                    <input type="hidden" name="id" value="{{ product.id }}">


                    {% hook 'product:single.form.start' %}

                    {% include 'pages.partials.product.options' %}

                    {% hook 'product:single.form.end' %}

                    <section class="flex bg-white p-5 sm:pb-0 rounded-md rounded-b-none">
                        <div class="center-between w-full">
                            <label class="form-label">
                                <b class="block">
                                    {{ trans('pages.products.price') }}
                                </b>
                            </label>

                            <div class="flex whitespace-nowrap price-wrapper gap-4 items-center">
                              <div class="{{ product.is_on_sale ? '' : 'hidden' }} price_is_on_sale space-x-2 rtl:space-x-reverse whitespace-nowrap">
                                <h2 class="total-price text-red-800 font-bold text-xl inline-block">{{product.sale_price|money}}</h2>
                                <span class="before-price text-gray-500 line-through">{{product.regular_price|money}}</span>
                              </div>
                              <div class="starting-or-normal-price gap-4 {{ product.is_on_sale ? 'hidden' : 'flex' }}">
                                {% if product.starting_price %}
                                  <span class="starting-price-title">{{ trans('pages.products.starting_price') }}</span>
                                {% endif %}
                                <h2 class="total-price font-bold text-xl inline-block"> {{product.starting_price ? product.starting_price|money : product.price|money }}</h2>
                              </div>
                            </div>

                            <div class="out-of-stock min-h-7 leading-7 hidden text-base text-red-600 !opacity-50 font-bold">{{ trans('pages.products.out_of_stock')}}</div>
                        </div>
                    </section>
                    <section class="sticky-product-bar bg-white p-5 rounded-md rounded-b-none">
                        {# Quantity #}
                        {% if product.is_hidden_quantity or product.type == 'booking' %}
                            <input type="hidden" value="1" name="quantity" aria-label="Quantity"/>
                        {% else %}
                            <div class="sticky-product-bar__quantity center-between mb-5">
                                <label class="form-label font-bold">
                                    {{ trans('pages.products.quantity') }}
                                </label>
                                <salla-quantity-input max="{{ product.max_quantity }}" value="1" name="quantity" aria-label="Quantity"
                                  class="border-gray-200 flex justify-end"></salla-quantity-input>
                            </div>
                        {% endif %}
                        {# Add to cart #}
                        <salla-add-product-button
                                quick-buy
                                {{theme.settings.get('sticky_add_to_cart') ? ' support-sticky-bar' : ''}}
                                {{product.is_require_shipping ? 'required-shipping' : ''}}
                                amount="{{ product.base_currency_price }}"
                                class="mt-5 w-full sticky-product-bar__btn"
                                product-status="{{ product.status }}"
                                product-type="{{ product.type }}"
                                product-id="{{ product.id }}"
                                loader-position="end"
                                type="submit"
                                width="wide"
                                {# Notify availability props#}
                                {% if product.notify_availability %}
                                    {{ product.notify_availability.subscribed ? 'is-subscribed' : '' }}
                                    channels="{{ product.notify_availability.channels|join(',') }}"
                                    subscribed-options="{{ product.notify_availability.subscribed_options|json_encode }}"
                                    {{ product.notify_availability.options ? 'notify-options-availability' : '' }}
                                {% endif %}>
                            {{ product.add_to_cart_label }}
                        </salla-add-product-button>
                    </section>
                    {% if product.is_giftable %}
                        <salla-gifting class="mt-5" widget-subtitle="{{ gifting_intro }}" product-id="{{ product.id }}" {{ product.is_require_shipping ? 'physical-products' : ''  }}></salla-gifting>
                    {% endif %}
                </form>

                {% hook 'product:single.description.end' %}
                <salla-quick-order class="mt-5 md:-mb-2 block"></salla-quick-order>
            </div>
        </div>
        <salla-offer></salla-offer>
    </div>

    {# Comments Area #}
    <salla-comments item-id="{{product.id}}" type="product"></salla-comments>

    {# Similar Products Area #}
    <div class="container gaming-product-detail">
        <salla-products-slider source="related" source-value="{{product.id}}" block-title="{{trans('pages.products.similar_products')}}" display-all-url></salla-products-slider>
    </div>
{% endblock %}

{% block scripts %}
    <script defer src="{{ 'product.js' | asset }}"></script>
    {% if product.has_3d_image %}
        <script type="module" defer src="https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js"></script>
    {% endif %}
{% endblock %}

