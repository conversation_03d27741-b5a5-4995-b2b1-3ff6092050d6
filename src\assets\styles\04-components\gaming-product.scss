/* Gaming Theme Product Detail Page Styles */

// Product Detail Page Enhancements
.gaming-product-detail {
  // Main container styling
  .container {
    @apply relative z-10;
  }

  // Product images slider
  .details-slider {
    @apply overflow-hidden rounded-xl;
    box-shadow: 0 0 25px rgba(0, 255, 255, 0.15);

    // Main image container
    .swiper-slide {
      @apply overflow-hidden rounded-xl;
      border: 1px solid rgba(255, 0, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }

      img {
        @apply object-cover;
      }
    }

    // Thumbnails styling
    .slide--one-fourth {
      @apply overflow-hidden rounded-lg;
      border: 1px solid rgba(0, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(255, 0, 255, 0.3);
      }

      &.swiper-slide-thumb-active {
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
        border-color: rgba(0, 255, 255, 0.5);
      }
    }

    // Navigation arrows
    .swiper-button-next,
    .swiper-button-prev {
      @apply bg-white bg-opacity-10 backdrop-blur-sm rounded-full;
      width: 40px;
      height: 40px;

      &:after {
        font-size: 18px;
        color: rgba(255, 0, 255, 0.8);
      }

      &:hover {
        background: rgba(255, 0, 255, 0.2);

        &:after {
          color: white;
        }
      }
    }
  }

  // Product info section
  .product-info {
    @apply bg-opacity-70 backdrop-blur-sm rounded-xl;
    background-color: rgba(20, 20, 35, 0.7);
    border: 1px solid rgba(128, 0, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    // Product title
    .product-title {
      @apply text-white font-bold text-2xl mb-4;
      text-shadow: 0 0 10px rgba(255, 0, 255, 0.4);
      position: relative;
      display: inline-block;

      &:after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 60px;
        height: 2px;
        background: linear-gradient(to right, rgba(255, 0, 255, 0.8), rgba(0, 255, 255, 0.8));
        box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
      }
    }

    // Product price
    .product-price {
      .total-price {
        @apply text-2xl font-bold;
        color: rgba(0, 255, 255, 0.9);
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      .before-price {
        @apply line-through text-gray-400;
      }
    }

    // Product description
    .product-description {
      @apply text-gray-200 leading-relaxed;

      p {
        @apply mb-4;
      }

      ul, ol {
        @apply pl-5 mb-4;

        li {
          @apply mb-2;
        }
      }
    }

    // Product options
    .product-options {
      @apply mt-6 p-4 rounded-lg;
      background-color: rgba(30, 30, 45, 0.5);
      border: 1px solid rgba(128, 0, 255, 0.2);

      .option-title {
        @apply text-white font-bold mb-2;
      }

      .form-label {
        @apply text-gray-300;
      }

      .form-input, .form-select {
        @apply bg-opacity-50 border-opacity-30 text-white;
        background-color: rgba(20, 20, 35, 0.7);
        border-color: rgba(0, 255, 255, 0.3);

        &:focus {
          border-color: rgba(255, 0, 255, 0.5);
          box-shadow: 0 0 0 2px rgba(255, 0, 255, 0.2);
        }
      }
    }

    // Add to cart button - using default styles
    .add-to-cart-btn {
      // No custom styles to allow default theme styles to apply
    }

    // Quantity selector
    .form-counter {
      @apply bg-opacity-50 rounded-lg overflow-hidden;
      background-color: rgba(20, 20, 35, 0.7);
      border: 1px solid rgba(0, 255, 255, 0.3);

      button {
        @apply text-white bg-opacity-30;
        background-color: rgba(128, 0, 255, 0.3);

        &:hover {
          background-color: rgba(255, 0, 255, 0.4);
        }
      }

      input {
        @apply text-white bg-transparent;
      }
    }
  }

  // Product tabs
  .product-tabs {
    @apply mt-8 bg-opacity-70 backdrop-blur-sm rounded-xl p-6;
    background-color: rgba(20, 20, 35, 0.7);
    border: 1px solid rgba(128, 0, 255, 0.2);

    .tabs-header {
      @apply border-b border-opacity-30 mb-6;
      border-color: rgba(0, 255, 255, 0.3);

      .tab-btn {
        @apply px-4 py-2 text-gray-400 font-medium;

        &.active {
          @apply text-white font-bold;
          border-bottom: 2px solid rgba(255, 0, 255, 0.8);
          box-shadow: 0 4px 6px -6px rgba(255, 0, 255, 0.6);
        }

        &:hover:not(.active) {
          @apply text-gray-200;
        }
      }
    }

    .tab-content {
      @apply text-gray-200;
    }
  }

  // Comments section
  .s-comments {
    @apply bg-opacity-50 backdrop-blur-sm;
    background-color: rgba(20, 20, 35, 0.5);

    .s-comments-container {
      @apply rounded-xl overflow-hidden;
    }

    .s-comments-title {
      @apply text-white font-bold;
      text-shadow: 0 0 10px rgba(255, 0, 255, 0.4);
    }
  }

  // Similar products
  .s-products-slider {
    .s-block-title {
      @apply text-white font-bold;
      text-shadow: 0 0 10px rgba(255, 0, 255, 0.4);
    }
  }
}
