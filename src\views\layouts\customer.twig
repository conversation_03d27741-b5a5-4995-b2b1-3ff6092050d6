{% extends "layouts.master" %}
{% block content %}
    <div class="profile-header gradient-bg">
        <div class="container relative flex justify-between h-full items-center">
            {# add breadcumbs container in pages to make a space in case breadcrumbs is off #}
            <nav class="breadcrumbs w-full py-5 self-start">
                <salla-breadcrumb></salla-breadcrumb>
            </nav>
        </div>
    </div>

    <div class="container lg:mb-24">
        <div class="flex items-start flex-col lg:flex-row ">
            <nav aria-label="Sidebar"
                 class="shrink-0 lg:sticky top-24 lg:-mt-28 lg:shadow-default rounded-md lg:bg-white w-full lg:w-72 rtl:lg:ml-8 ltr:lg:mr-8 lg:pt-8 lg:pb-11 sidebar space-y-px">

                {% if is_page('customer.profile') %}
                    <div class="text-center md:mb-9">
                        <div class="h-20 w-20 m-auto avatar-wrap bg-gray-100 rounded-full">
                            <salla-file-upload profile-image value="{{ user.avatar }}">
                                <span class='avatar-placeholder flex justify-center items-center flex-col'>
                                    <span class='sicon-user'></span>
                                    <span class='text'>{{ trans('common.uploader.choose_suitable_picture') }}</span>
                                </span>
                            </salla-file-upload>
                        </div>
                    </div>
                {% endif %}

                <div class="hidden lg:block">
                    <salla-user-menu inline></salla-user-menu>
                </div>
            </nav>
            <div class="main-content w-full flex-1 lg:pt-10 ">
                {% block inner_title %}
                    <h1 class="font-bold text-lg text-center sm:rtl:text-right sm:ltr:text-left mb-2">{{ page.title }}</h1>
                {% endblock %}
                {#
                All customer pages have same header, this layout will wrap the different content.
                Use it like: `{% extends "layouts.customer" %}` instead of `{% extends "layouts.master" %}`
                Then body: `{% block inner_content %}` instead of `{% block content %}`
                #}
                {% block inner_content %}{% endblock %}
            </div>
        </div>
    </div>
{% endblock %}
