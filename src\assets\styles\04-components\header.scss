/* Top Nav */
.top-navbar {
  @apply flex py-2 min-h-[48px];

  @screen lg {
    @apply py-1.5;
  }

  .topnav-has-bg & {
    @apply bg-[color:var(--topnav-bg)];
  }

  .topnav-has-gradient & {
    @apply bg-gradient-to-r from-[color:var(--topnav-gradient-from)] to-[color:var(--topnav-gradient-to)];
  }

  .topnav-has-text-color & {
    @apply text-[color:var(--topnav-text-color)];
  }

  .s-search-input{
    @apply bg-gray-200/50 hover:bg-gray-200/70 border-none;
  }

  @media (max-width: 640px) {
    .s-search-results{
      @apply w-screen max-w-[100vw] rtl:-left-2.5 ltr:-right-2.5; 
    }
  } 

  .topnav-is-dark & {
    @apply bg-dark text-gray-300;

    .btn--circle-gray,
    .btn--rounded-gray,
    .s-search-input{
      @apply bg-gray-100/10 hover:bg-gray-100/[0.15]
    }

    .topnav-link-item{
      @apply border-gray-300/10;
    }

    .s-search-input{
      @apply text-white;
    }
  }

  .search-btn{
    @apply grow sm:grow-0 justify-start md:justify-center;
  }
  
  /* Gaming theme specific styles for top navbar */
  .gaming-theme & {
    @apply bg-[#0a0a18] text-gray-100;
    border-bottom: 1px solid rgba(111, 76, 255, 0.2);
    box-shadow: 0 0 15px rgba(111, 76, 255, 0.2);
    backdrop-filter: blur(10px);
    
    .btn--circle-gray,
    .btn--rounded-gray {
      @apply bg-gray-800/50 text-gray-200 hover:bg-gray-700/60 border border-purple-500/20;
      transition: all 0.3s ease;
      box-shadow: 0 0 8px rgba(111, 76, 255, 0.2);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 0 12px rgba(111, 76, 255, 0.4);
      }
    }
    
    .s-search-input {
      @apply bg-gray-800/40 text-white border-none placeholder-gray-400;
      box-shadow: 0 0 10px rgba(111, 76, 255, 0.15) inset, 0 0 8px rgba(111, 76, 255, 0.15);
      
      &:focus {
        box-shadow: 0 0 0 2px rgba(111, 76, 255, 0.3), 0 0 10px rgba(111, 76, 255, 0.2) inset;
      }
    }
    
    .topnav-link-item {
      @apply border-purple-500/20 text-gray-300 hover:text-white;
      transition: all 0.3s ease;
      
      &:hover {
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
      }
    }
  }
}

// contacs menu items - pages menu items
.topnav-link-item{
  @apply inline-block transition duration-300 px-4 rtl:last:pl-0 ltr:last:pr-0 py-px text-sm leading-none ltr:border-r rtl:border-l border-gray-200 ltr:last:border-0 rtl:last:border-0 hover:opacity-80;

  &.right-side{
    @apply rtl:first:pr-0 ltr:first:pl-0
  }
}

/* Main Nav */
.main-nav-container {
  @apply min-h-[68px] lg:min-h-[84px];
  
  // Custom background Color
  .has-bg &,
  .has-bg & .sub-menu{
    @apply bg-[color:var(--mainnav-bg)];
  }

  // Custom text color
  .has-text-color & {
    @apply text-[color:var(--mainnav-text-color)];
  }
  
  /* Gaming theme specific styles for main navbar */
  .gaming-theme & {
    @apply bg-[#0a0a18] text-white shadow-xl;
    border-bottom: 1px solid rgba(111, 76, 255, 0.3);
    backdrop-filter: blur(10px);
    
    .inner {
      @apply bg-[#0a0a18] !important;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    .navbar-brand img {
      filter: drop-shadow(0 0 8px rgba(111, 76, 255, 0.5));
      transition: all 0.3s ease;
      
      &:hover {
        transform: scale(1.05);
      }
    }
    
    .header-btn__icon {
      @apply bg-gray-800/60 text-gray-200 border-purple-500/30;
      transition: all 0.3s ease;
      box-shadow: 0 0 10px rgba(111, 76, 255, 0.2);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 0 15px rgba(111, 76, 255, 0.4);
        @apply text-white;
      }
    }
    
    .s-cart-summary-count {
      @apply bg-purple-600;
      box-shadow: 0 0 8px rgba(111, 76, 255, 0.8);
    }
    
    salla-cart-summary {
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 5px;
        bottom: -5px;
        left: 0;
        background: linear-gradient(90deg, transparent, rgba(111, 76, 255, 0.5), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      &:hover::after {
        opacity: 1;
      }
    }
  }
}

.menu-item {
  @apply flex items-center px-6 py-2.5 sm:text-sm text-gray-500 transition-colors duration-300 hover:bg-gray-200/30;

  &.logout{
    @apply text-red-400;
  }

  &.is-active{
    @apply text-primary bg-gray-200/20;
  }
  
  /* Gaming theme specific styles for menu items */
  .gaming-theme & {
    @apply text-gray-300 hover:bg-gray-800/60 hover:text-white;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 0 15px rgba(111, 76, 255, 0.2) inset;
      text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
    }
    
    &.is-active {
      @apply bg-gray-800/40 text-white;
      box-shadow: 0 0 15px rgba(111, 76, 255, 0.3) inset;
      border-left: 2px solid rgba(111, 76, 255, 0.8);
      text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
    }
    
    &.logout {
      @apply text-red-400 hover:text-red-300;
      
      &:hover {
        box-shadow: 0 0 15px rgba(255, 76, 76, 0.2) inset;
        text-shadow: 0 0 8px rgba(255, 76, 76, 0.5);
      }
    }
  }
}

/* Sticky Header */
.main-nav-container {
  &.animated {
    .inner {
      transition: top 0.5s, transform 0.5s, -webkit-transform 0.5s, opacity 0.4s;
    }
  }

  &.fixed-pinned {
    .inner {
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      width: 100%;
      z-index: 29;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
      transform: translate3d(0, -100%, 0);

      @media (max-width: 1024px){
        transform: none;
        top: -70px;
      }
      
      /* Gaming theme specific sticky header */
      .gaming-theme & {
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3), 0 0 15px rgba(111, 76, 255, 0.3);
        border-bottom: 1px solid rgba(111, 76, 255, 0.4);
      }
    }

    .navbar-brand {
      img {
        max-height: 40px;
      }

      h4 {
        line-height: 1;
      }
    }

    .main-menu > li > a {
      padding-top: 20px;
      padding-bottom: 20px;
    }
  }

  &.fixed-header {
    .inner {
      transform: translate3d(0, 0, 0);

      @media (max-width: 1024px){
        transform: none;
        top: 0;
      }
    }
  }
}

/* Categories Filter Container - Gaming Theme */
.gaming-theme .categories-filter-container {
  @apply bg-[#0a0a18] border-t border-purple-500/20;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) inset;
  
  .categories-filter-wrapper {
    position: relative;
    overflow: hidden;
    
    .gaming-glow-line {
      position: absolute;
      top: 0;
      left: -100%;
      width: 30%;
      height: 2px;
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(111, 76, 255, 0.2) 20%,
        rgba(111, 76, 255, 0.5) 50%,
        rgba(111, 76, 255, 0.2) 80%,
        transparent 100%);
      box-shadow: 0 0 20px 3px rgba(111, 76, 255, 0.4);
      animation: categoryGlow 6s infinite ease-in-out;
      z-index: 2;
    }
    
    @keyframes categoryGlow {
      0% { left: -30%; }
      100% { left: 100%; }
    }
  }
  
  .category-icon {
    @apply bg-gray-800/50 border-purple-500/20;
    transition: all 0.3s ease;
    
    &:hover, &.active {
      @apply border-purple-500/40;
      transform: translateY(-3px);
      box-shadow: 0 8px 15px rgba(111, 76, 255, 0.2);
    }
    
    .category-icon-image {
      @apply bg-gray-900/60;
      box-shadow: 0 0 10px rgba(111, 76, 255, 0.2) inset;
    }
    
    .category-icon-name {
      @apply text-gray-300;
    }
    
    &:hover .category-icon-name, 
    &.active .category-icon-name {
      text-shadow: 0 0 8px rgba(111, 76, 255, 0.8);
    }
  }
}

.navbar-brand {
  @apply items-center flex my-2 lg:my-0;

  img {
    @apply w-auto max-h-12 max-w-[100px] xs:max-w-[170px];
  }
}

// Mainnav cart icon
.header-btn{
  @apply border-none outline-none transition-opacity hover:opacity-80;

  &__icon{
    @apply text-xl w-10 h-10 rounded-full border border-gray-200 flex items-center justify-center text-gray-700;
    &.icon{
      @apply mr-[9px] rtl:ml-[9px] rtl:mr-[unset];
    }
  }
}

salla-user-menu{
  @apply shrink-0;
}

// cart summary
.s-cart-summary-total{
  @apply text-black font-[600];
  
  .gaming-theme & {
    @apply text-white;
    text-shadow: 0 0 8px rgba(111, 76, 255, 0.5);
  }
}

.s-cart-summary-count{
  @apply -top-0.5 ltr:-left-1.5 rtl:-right-1.5 bg-red-800;
}

#nav-cart{
  @apply flex items-center rtl:mr-4 ltr:ml-4 relative whitespace-nowrap;

  .icon{
    @apply rtl:ml-2 ltr:mr-2;
  }

  span{
    @apply absolute top-1 rtl:-right-2 ltr:-left-2;
  }
}


// Search
.header-search{
  .s-search-results{
    @apply z-10;
    
    .gaming-theme & {
      @apply bg-gray-900/95 border border-purple-500/30;
      backdrop-filter: blur(10px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(111, 76, 255, 0.2);
      
      .s-search-result-item {
        @apply border-b border-purple-500/20 text-white;
        
        &:hover {
          @apply bg-purple-900/20;
        }
      }
    }
  }
}

/* Gaming Theme Search Container */
.gaming-theme {
  .gaming-search-container {
    position: relative;
    margin: 0 10px;
    overflow: visible;
    z-index: 9999;
    
    &::before {
      content: '';
      position: absolute;
      top: -5px;
      bottom: -5px;
      left: -5px;
      right: -5px;
      background: linear-gradient(90deg, 
        rgba(111, 76, 255, 0.1),
        rgba(111, 76, 255, 0.2),
        rgba(111, 76, 255, 0.1));
      border-radius: 25px;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
    }
    
    &:hover::before, &:focus-within::before {
      opacity: 1;
    }
    
    .gaming-search-glow {
      position: absolute;
      top: 0;
      bottom: 0;
      left: -100%;
      width: 60%;
      background: linear-gradient(90deg, 
        transparent,
        rgba(111, 76, 255, 0.2),
        transparent);
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 1;
      animation: searchGlow 4s infinite ease-in-out;
    }
    
    &:hover .gaming-search-glow, &:focus-within .gaming-search-glow {
      opacity: 1;
    }
    
    @keyframes searchGlow {
      0% { left: -60%; }
      100% { left: 100%; }
    }
  }
  
  category-search {
    input {
      @apply bg-gray-800/70 text-white border-purple-500/20;
      border-radius: 20px;
      padding: 8px 15px;
      transition: all 0.3s ease;
      box-shadow: 0 0 10px rgba(111, 76, 255, 0.1) inset;
      
      &:focus, &:hover {
        @apply border-purple-500/40;
        box-shadow: 0 0 15px rgba(111, 76, 255, 0.2) inset;
      }
      
      &::placeholder {
        @apply text-gray-400;
      }
    }
    
    .search-icon {
      color: rgba(111, 76, 255, 0.6);
      transition: all 0.3s ease;
      
      &:hover {
        color: rgba(111, 76, 255, 1);
        transform: scale(1.1);
      }
    }
  }
}