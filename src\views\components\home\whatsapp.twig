{#
| Variable  | Type    | Description                                |
|-----------|---------|-------------------------------------------|
| whatsapp  | string  | WhatsApp phone number with country code   |
#}

{# Try to get the WhatsApp number from various possible sources #}
{% set phone_number = null %}

{# Check different possible variable names #}
{% if whatsapp is defined and whatsapp is not empty %}
    {% set phone_number = whatsapp %}
{% elseif phone is defined and phone is not empty %}
    {% set phone_number = phone %}
{% elseif component is defined and component.whatsapp is defined and component.whatsapp is not empty %}
    {% set phone_number = component.whatsapp %}
{% else %}
    {% set phone_number = '966500000000' %}
{% endif %}

{# Debug info (will be hidden but helpful for troubleshooting) #}
<div style="display: none;" id="whatsapp-debug-info">
  Direct whatsapp var: {{ whatsapp is defined ? whatsapp : 'undefined' }}<br>
  From component: {{ component is defined and component.whatsapp is defined ? component.whatsapp : 'undefined' }}<br>
  Using: {{ phone_number }}
</div>

<a href="https://wa.me/{{ phone_number|replace({'+': '', ' ': ''}) }}?text={{ "مرحباً، أود الاستفسار عن منتجاتكم"|url_encode }}" target="_blank" class="gaming-whatsapp-float tooltip-toggle--hover icon-trigger" id="whatsapp-float-button" aria-label="Chat on WhatsApp">
    <div class="tooltip-content gaming-tooltip">تحدث معنا عبر الواتساب</div>
    <div class="whatsapp-icon gaming-whatsapp-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="white">
            <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
        </svg>
    </div>
    <div class="gaming-whatsapp-pulse"></div>
    <div class="gaming-whatsapp-glow"></div>
</a>

<style>
/* Gaming WhatsApp Float Button Styles */
.gaming-whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #25D366, #128C7E);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    border: 2px solid rgba(255, 0, 255, 0.3);
    overflow: visible;

    &:hover {
        transform: translateY(-5px) scale(1.1);
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.6),
                    0 0 20px rgba(255, 0, 255, 0.5);
        border-color: rgba(0, 255, 255, 0.6);
    }
}

.gaming-whatsapp-icon {
    width: 30px;
    height: 30px;
    position: relative;
    z-index: 2;

    svg {
        width: 100%;
        height: 100%;
        filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
    }
}

/* Pulse animation */
.gaming-whatsapp-pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px solid rgba(255, 0, 255, 0.6);
    animation: gaming-pulse 2s infinite;
    z-index: 1;
}

/* Glow effect */
.gaming-whatsapp-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.2) 0%, transparent 70%);
    animation: gaming-glow 3s infinite alternate;
    z-index: 0;
}

/* Tooltip styling */
.gaming-tooltip {
    position: absolute !important;
    bottom: 70px !important;
    left: 50% !important;
    right: auto !important;
    top: auto !important;
    transform: translateX(-50%) !important;
    background: rgba(20, 20, 35, 0.95) !important;
    color: white !important;
    padding: 10px 15px !important;
    border-radius: 12px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(255, 0, 255, 0.4) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                0 0 15px rgba(255, 0, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3) !important;
    min-width: 140px !important;
    max-width: 200px !important;
    text-align: center !important;
    z-index: 9999 !important;

    &:before {
        content: '' !important;
        position: absolute !important;
        top: 100% !important;
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%) !important;
        border: 8px solid transparent !important;
        border-top-color: rgba(20, 20, 35, 0.95) !important;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
    }

    &:after {
        content: '' !important;
        position: absolute !important;
        top: 100% !important;
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%) !important;
        border: 6px solid transparent !important;
        border-top-color: rgba(255, 0, 255, 0.4) !important;
    }
}

.gaming-whatsapp-float:hover .gaming-tooltip {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(-50%) translateY(-5px) !important;
}

/* Animations */
@keyframes gaming-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes gaming-glow {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .gaming-whatsapp-float {
        width: 55px;
        height: 55px;
        bottom: 15px;
        right: 15px;
    }

    .gaming-whatsapp-icon {
        width: 25px;
        height: 25px;
    }

    .gaming-tooltip {
        font-size: 11px;
        padding: 8px 12px;
        bottom: 65px;
        min-width: 120px;

        &:before {
            border-width: 6px;
        }

        &:after {
            border-width: 4px;
        }
    }
}

/* RTL support */
[dir="rtl"] .gaming-whatsapp-float {
    right: auto;
    left: 20px;
}

/* RTL tooltip positioning is handled by the centered approach above */

@media (max-width: 768px) {
    [dir="rtl"] .gaming-whatsapp-float {
        left: 15px;
    }
}
</style>

<script>
    (function() {
        // Function to extract WhatsApp number from Salla component and update the link
        function updateWhatsAppLink() {
            const whatsappBtn = document.getElementById('whatsapp-float-button');
            if (!whatsappBtn) return;

            try {
                // Try to get the components from Salla's script tags
                const scriptTags = document.querySelectorAll('script[id^="twilight-settings"]');

                for (const script of scriptTags) {
                    try {
                        // Parse the JSON data
                        const data = JSON.parse(script.textContent);

                        // Look for whatsapp component
                        if (data && data.components) {
                            for (const component of data.components) {
                                if (component.key && component.path === 'home.whatsapp') {
                                    // Found the WhatsApp component, now look for the phone number
                                    for (const field of component.fields || []) {
                                        if (field.id === 'whatsapp' && field.value) {
                                            // Found the phone number
                                            const number = field.value.toString().replace(/[\+\s]/g, '');
                                            const text = "مرحباً، أود الاستفسار عن منتجاتكم";

                                            // Update the href with the correct number
                                            whatsappBtn.href = `https://wa.me/${number}?text=${encodeURIComponent(text)}`;
                                            console.log('Updated WhatsApp number from Salla component:', field.value);
                                            return; // Exit after successful update
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing script data:', e);
                    }
                }

                // If we got here, try to check for a global variable
                if (window.twilightSettings && window.twilightSettings.components) {
                    const components = window.twilightSettings.components;

                    for (const component of components) {
                        if (component.path === 'home.whatsapp' && component.whatsapp) {
                            const number = component.whatsapp.toString().replace(/[\+\s]/g, '');
                            const text = "مرحباً، أود الاستفسار عن منتجاتكم";
                            whatsappBtn.href = `https://wa.me/${number}?text=${encodeURIComponent(text)}`;
                            console.log('Found WhatsApp number in twilightSettings:', component.whatsapp);
                            return;
                        }
                    }
                }
            } catch (e) {
                console.error('Error in WhatsApp link update:', e);
            }
        }

        // Execute when DOM is loaded
        document.addEventListener('DOMContentLoaded', updateWhatsAppLink);

        // Also add a fallback to run after a delay (in case components load later)
        setTimeout(updateWhatsAppLink, 2000);
        setTimeout(updateWhatsAppLink, 5000);
    })();
</script>