{#
| Variable                | Type     | Description                                                                  |
|-------------------------|----------|------------------------------------------------------------------------------|
| component               | object   | Contains merchant settings for fields from twilight.json `component` section |
| component.banner        | object[] | list of banners                                                              |
| component.banner[].img  | string   | Banner image URL                                                             |
| component.banner[].url  | object   | Banner link (variable-list format)                                           |
| component.banner[].title| ?string  | Banner title                                                                 |
| component.banner[].subtitle| ?string | Banner subtitle/description                                                |
| position                | int      | Sorting number start from zero                                               |
#}

<section class="s-block s-block--new-banners wide-placeholder gaming-banners-section" id="gaming-banners-section-{{ position }}">
    {% if component.banner|length %}
        <salla-slider
            id="gaming-banners-{{ position }}"
            type="fullwidth"
            auto-play
            pagination
            show-controls="true"
            class="gaming-theme-slider">
            <div slot="items">
                {% for banner in component.banner %}
                    <div class="swiper-slide gaming-banner-slide">
                        <a href="{{ banner.url }}" class="gaming-banner-link">
                            <div class="gaming-banner-image-container">
                                <img src="{{ banner.img }}" alt="{{ banner.title|default('Banner ' ~ loop.index) }}" class="gaming-banner-image">

                                <div class="gaming-banner-overlay"></div>

                                {% if banner.title or banner.subtitle %}
                                    <div class="gaming-banner-content">
                                        {% if banner.title %}
                                            <h3 class="gaming-banner-title animate-element">{{ banner.title }}</h3>
                                        {% endif %}

                                        {% if banner.subtitle %}
                                            <p class="gaming-banner-subtitle animate-element">{{ banner.subtitle }}</p>
                                        {% endif %}

                                        <div class="gaming-banner-button animate-element">
                                            <span class="btn-text">اكتشف الآن</span>
                                            <span class="btn-icon">
                                                <i class="sicon-arrow-{{ salla.config.get('theme.is_rtl') ? 'left' : 'right' }}"></i>
                                            </span>
                                        </div>
                                    </div>
                                {% endif %}

                                <!-- Animated Elements -->
                                <div class="gaming-banner-effects">
                                    {% for i in 1..5 %}
                                        <div class="gaming-particle particle-{{ i }}" style="--delay: {{ i * 0.5 }}s;"></div>
                                    {% endfor %}
                                    <div class="gaming-glow"></div>
                                </div>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>
        </salla-slider>
    {% endif %}
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Intersection Observer for animation when element comes into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');

                    // Animate text elements with delay
                    const animateElements = entry.target.querySelectorAll('.animate-element');
                    animateElements.forEach((element, index) => {
                        element.style.transitionDelay = `${0.3 + (index * 0.2)}s`;
                        element.classList.add('animate-in');
                    });

                    // Unobserve after animation is triggered
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.2 // Trigger when 20% of the element is visible
        });

        // Observe the banner section
        const bannerSection = document.getElementById('gaming-banners-section-{{ position }}');
        if (bannerSection) {
            observer.observe(bannerSection);
        }
    });
</script>

<style>
    /* Gaming Theme Banner Styles */
    .gaming-theme-slider {
        margin-bottom: 2rem;
    }

    /* Pop-up Animation for the entire component */
    .gaming-banners-section {
        opacity: 0;
        transform: scale(0.95);
        transition: opacity 0.8s ease, transform 0.8s ease;
    }

    .gaming-banners-section.animate-in {
        opacity: 1;
        transform: scale(1);
    }

    .gaming-banner-slide {
        height: 500px;
        position: relative;
        overflow: hidden;
    }

    .gaming-banner-image-container {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .gaming-banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 1s ease;
    }

    .gaming-banner-link:hover .gaming-banner-image {
        transform: scale(1.05);
    }

    .gaming-banner-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to top,
            rgba(10, 10, 15, 0.9) 0%,
            rgba(10, 10, 15, 0.6) 40%,
            rgba(10, 10, 15, 0.3) 70%,
            rgba(10, 10, 15, 0.1) 100%);
        z-index: 1;
    }

    .gaming-banner-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 3rem;
        z-index: 2;
        color: #fff;
        text-align: start;
        transform: translateY(0);
        transition: transform 0.5s ease;
    }

    .gaming-banner-link:hover .gaming-banner-content {
        transform: translateY(-10px);
    }

    /* Text Animation Elements */
    .animate-element {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.8s ease, transform 0.8s ease, color 0.3s ease;
        transition-delay: 0s; /* Will be overridden by JS */
    }

    .animate-element.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    .gaming-banner-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
        color: #fff;
        position: relative;
        display: inline-block;
    }

    .gaming-banner-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 0; /* Start with 0 width */
        height: 3px;
        background: var(--color-primary, #1DE9B6);
        box-shadow: 0 0 10px var(--color-primary, #1DE9B6),
                    0 0 20px var(--color-primary, #1DE9B6);
        transition: width 0.8s ease 0.8s; /* Delay the line animation */
    }

    .gaming-banner-title.animate-in::after {
        width: 60px; /* Expand to full width when animated */
    }

    .gaming-banner-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        text-shadow: 0 1px 5px rgba(0, 0, 0, 0.7);
        margin-bottom: 1.5rem;
        max-width: 600px;
    }

    .gaming-banner-button {
        display: inline-flex;
        align-items: center;
        background: rgba(29, 233, 182, 0.2);
        border: 1px solid var(--color-primary, #1DE9B6);
        color: #fff;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 0 15px rgba(29, 233, 182, 0.3);
        cursor: pointer;
    }

    .gaming-banner-button .btn-icon {
        margin-right: 10px;
        margin-left: 10px;
        transition: transform 0.3s ease;
    }

    .gaming-banner-link:hover .gaming-banner-button {
        background: rgba(29, 233, 182, 0.3);
        box-shadow: 0 0 20px rgba(29, 233, 182, 0.5);
    }

    .gaming-banner-link:hover .gaming-banner-button .btn-icon {
        transform: translateX(5px);
    }

    /* Animated Elements */
    .gaming-banner-effects {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        pointer-events: none;
    }

    .gaming-particle {
        position: absolute;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, rgba(29, 233, 182, 0.2) 0%, rgba(29, 233, 182, 0) 70%);
        border-radius: 50%;
        animation: float 15s infinite ease-in-out;
        animation-delay: var(--delay, 0s);
        opacity: 0;
    }

    .particle-1 {
        top: 20%;
        left: 10%;
        width: 150px;
        height: 150px;
    }

    .particle-2 {
        top: 60%;
        left: 20%;
        width: 100px;
        height: 100px;
    }

    .particle-3 {
        top: 30%;
        right: 15%;
        width: 120px;
        height: 120px;
    }

    .particle-4 {
        top: 70%;
        right: 25%;
        width: 80px;
        height: 80px;
    }

    .particle-5 {
        top: 40%;
        left: 50%;
        width: 180px;
        height: 180px;
    }

    .gaming-glow {
        position: absolute;
        bottom: -50px;
        left: 0;
        right: 0;
        height: 100px;
        background: radial-gradient(ellipse at center, rgba(29, 233, 182, 0.2) 0%, rgba(29, 233, 182, 0) 70%);
        filter: blur(20px);
        opacity: 0.7;
    }

    @keyframes float {
        0% {
            transform: translateY(0) translateX(0);
            opacity: 0;
        }
        20% {
            opacity: 0.5;
        }
        50% {
            transform: translateY(-20px) translateX(10px);
            opacity: 0.7;
        }
        80% {
            opacity: 0.5;
        }
        100% {
            transform: translateY(0) translateX(0);
            opacity: 0;
        }
    }

    /* Pop-in animation for text elements */
    @keyframes popIn {
        0% {
            opacity: 0;
            transform: scale(0.8) translateY(20px);
        }
        70% {
            transform: scale(1.05) translateY(-5px);
        }
        100% {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    /* Responsive Adjustments */
    @media (max-width: 1024px) {
        .gaming-banner-slide {
            height: 450px;
        }

        .gaming-banner-title {
            font-size: 2rem;
        }

        .gaming-banner-subtitle {
            font-size: 1.1rem;
        }
    }

    @media (max-width: 768px) {
        .gaming-banner-slide {
            height: 350px;
        }

        .gaming-banner-title {
            font-size: 1.75rem;
        }

        .gaming-banner-subtitle {
            font-size: 1rem;
        }

        .gaming-banner-content {
            padding: 2rem;
        }

        .gaming-banner-button {
            padding: 0.6rem 1.2rem;
        }
    }

    @media (max-width: 480px) {
        .gaming-banner-slide {
            height: 300px;
        }

        .gaming-banner-title {
            font-size: 1.5rem;
        }

        .gaming-banner-subtitle {
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .gaming-banner-content {
            padding: 1.5rem;
        }

        .gaming-banner-button {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
    }
</style>