import "lite-youtube-embed";
import BasePage from "./base-page";
import Lightbox from "fslightbox";
import { initGamingGallery } from "./partials/gaming-gallery";
window.fslightbox = Lightbox;

class Home extends BasePage {
    onReady() {
        this.initFeaturedTabs();
        this.initGamingComponents();
    }

    /**
     * used in views/components/home/<USER>
     */
    initFeaturedTabs() {
        app.all('.tab-trigger', el => {
            el.addEventListener('click', ({ currentTarget: btn }) => {
                let id = btn.dataset.componentId;
                // btn.setAttribute('fill', 'solid');
                app.toggleClassIf(`#${id} .tabs-wrapper>div`, 'is-active opacity-0 translate-y-3', 'inactive', tab => tab.id == btn.dataset.target)
                    .toggleClassIf(`#${id} .tab-trigger`, 'is-active', 'inactive', tabBtn => tabBtn == btn);

                // fadeIn active tabe
                setTimeout(() => app.toggleClassIf(`#${id} .tabs-wrapper>div`, 'opacity-100 translate-y-0', 'opacity-0 translate-y-3', tab => tab.id == btn.dataset.target), 100);
            })
        });
        document.querySelectorAll('.s-block-tabs').forEach(block => block.classList.add('tabs-initialized'));
    }

    /**
     * Initialize gaming theme components
     */
    initGamingComponents() {
        // Initialize modules that need to be accessible globally
        if (typeof salla.modules === 'undefined') {
            salla.modules = {};
        }
        
        if (typeof salla.modules.gaming === 'undefined') {
            salla.modules.gaming = {};
        }
        
        // Initialize gaming gallery
        initGamingGallery();
    }
}

Home.initiateWhenReady(['index']);