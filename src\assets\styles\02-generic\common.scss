html {
  scroll-behavior: smooth;
}

// using salla.document.onClick(...), become effective when all children are none pointer events.
.cursor-pointer * {
  pointer-events: none;
}

.flip-x{
  transform: scaleX(-1);
}

input[type="file"] {
  display: none;
}

body{
  @apply bg-gray-50;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100vw; /* Ensure content doesn't exceed viewport width */

  /* Hide scrollbar but allow scrolling */
  &::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari and Opera */
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */

  [type='text'], [type='email'], [type='url'], [type='password'], [type='number'], [type='date'], [type='datetime-local'], [type='month'], [type='search'], [type='tel'], [type='time'], [type='week'], [multiple], textarea, select{
    @apply focus:ring-0;
  }
}

.text-start{
	@apply rtl:text-right ltr:text-left;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

[type='submit']{
  -webkit-appearance: listitem; // safari fix, none: disable css on web component
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.unicode {
  unicode-bidi: plaintext;
}

.spinner-loader {
  border-right-color: var(--color-primary) !important;

  &.reverse {
    border-right-color: #9f7171 !important;
    background-color: #f98181;
  }
}

.hide-scroll {
  /* hide scrollbar but allow scrolling */
  -ms-overflow-style: none;
  /* for Internet Explorer, Edge */
  scrollbar-width: none;
  /* for Firefox */
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
    /* for Chrome, Safari, and Opera */
  }
}

.remove-item-btn:after {
  content: "\ea47";
  font-family: "sallaicons" !important;
  // speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.cart-thumb {
  position: fixed;
  z-index: 30;
  transition: all 0.3s cubic-bezier(0.2, 1, 0.3, 1);
}


/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

// hide chat tags
body {

  &.fslightbox-open,
  &.modal-open {

    #tidio-chat-iframe,
    .fb_reset,
    [id^=gb-widget] {
      display: none !important;
    }
  }

  // &.modal-open {
  //   #tidio-chat-iframe, .fb_reset, [id^=gb-widget] {
  //     display: none !important;
  //   }
  // }
}


// Loading Btn state
.loader {
  &:before {
    content: '';
    opacity: 1;
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid;
    border-top-color: var(--color-primary);
    border-bottom-color: #eee;
    border-left-color: var(--color-primary);
    border-right-color: #eee;
    animation: loader 1s ease-in-out infinite;

    .btn--danger & {
      border-top-color: #ff6767;
      border-left-color: #ff6767;
    }
  }

  &--small {
    &:before {
      width: 16px;
      height: 16px;
    }
  }
}

@keyframes loader {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}


/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
.animated {
  animation-duration: var(--animate-duration);
  animation-fill-mode: both;
}

@keyframes pulse-anime {
  from {
    transform: scale3d(1, 1, 1);
  }

  50% {
    transform: scale3d(1.05, 1.05, 1);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}

.pulse-anime {
  animation-name: pulse-anime;
  animation-timing-function: ease-in-out;
}


/*
** Single page
*/
.content {
  .content-entry {
    img {
      margin: 15px 0 25px;
    }
     ul , li{
      list-style: inherit !important;
    }
     ol{
      list-style: auto !important;
    }
  }
}

/* Components Init*/

salla-modal {
  &:not(.hydrated) { // hide modal component till it was loaded
    display: none;
  }
}


/* video aspect ratio*/
.videoWrapper {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  height: 0;

  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    max-width: initial;
  }
}

// grid trigger
.grid-trigger{
  @apply cursor-pointer mx-1 text-xl w-10 h-10 rounded-md flex items-center justify-center text-gray-400;
}


// center classes
.flex-center{
  @apply flex justify-center items-center;
}

.center-between{
  @apply flex justify-between items-center;
}

.grow-0{
  flex-grow: 0 !important;
}

.basis-0{
  flex-basis: 0;
}

// Gradient Background
.gradient-bg{
  @apply from-primary to-primary-d;

  [dir="ltr"] & {
    @apply bg-gradient-to-r;
  }

  [dir="rtl"] & {
    @apply bg-gradient-to-l;
  }

  @media (max-width: 1024px){
    &.profile-header{
      background-image: none;

      .breadcrumbs {
        a,
        span,
        .arrow {
          @apply text-gray-600;
        }
      }
    }
  }
}

// Small badge
.badge {
  @apply p-1 min-w-[20px] h-5 rounded-full inline-flex items-center justify-center text-xs;

  &--red{
    @apply bg-red-400 text-white;
  }
}


// copy icon
.copy-icon{
  @apply text-xs;
}

.s-localization-modal-inner {
  width: 100%;
}

// fix rating modal when there is multiple products in mobile
.s-rating-modal-wrap .s-modal-body{
  position: relative !important;
}

.s-verify-back {
  top: 0.5rem;
  svg {
    max-width: 18px;
    fill: rgba(107,114,128,var(--tw-text-opacity));
  }
}

[dir=rtl] .s-verify-back {
  right: 0.5rem;
}

[dir=ltr] .s-verify-back {
  left: 0.5rem;
}

@media (max-width: 639px){
  .s-verify-back {
    display: none;
  }
}

.article {
  &--main {
    iframe {
      width: 100%;
      height: 30vw;
      @media (max-width: 639px){
        height: 50vw;
      }
    }
  }
}

// cart submit actions buttons
.cart-submit-wrap{
  @apply flex ltr:flex-wrap justify-between items-center -mx-[5px] gap-2.5;

  > *{
    @apply grow mx-[5px];
  }
}

.s-button-btn, .s-price-range-number-input{
  @apply rounded;
}

// salla app install banner
.s-app-install-banner {
  @apply hidden flex-row items-center fixed left-0 right-0 w-[95%] my-4 mx-auto z-[999] rounded-md p-3 gap-2 drop-shadow-md;
  background-color:  color-mix(in srgb, var(--color-primary) 15%, white);
  &[open=false] {
    @apply hidden;
  }
  &[open=true][position=top] {
    @apply flex transform duration-500;
  }
  &[open=true][position=bottom] {
    @apply flex animate-slideUpFromBottom;
  }
  &[position=top] {
    @apply top-0 relative m-0 w-full rounded-none;
  }
  &[position=bottom] {
    @apply bottom-0;
  }
  &[closing][position=bottom] {
    @apply animate-slideDownFromBottom;
    animation-fill-mode: forwards;
  }
  &-title {
    @apply text-base font-bold text-primary;
  }
  &-sub-title {
    @apply text-sm font-normal max-w-[95%];
  }
  &-cta {
    @apply text-primary underline;
  }
  &-cancel-button {
    @apply absolute top-2 rtl:left-3 ltr:right-3;
  }
}

.blog-category {
  ol,ul,li {
    list-style: auto;
  }

  a {
    @apply text-primary hover:text-primary-d;
  }
}

/* Gaming Theme Specific Styles */
.gaming-theme {
  /* Base background for body with fallback if canvas doesn't work */
  background-color: #0a0a0f;

  /* Gaming theme typography */
  h1, h2, h3, h4, h5, h6 {
    font-weight: bold;
    letter-spacing: 0.5px;
  }

  /* Add neon glow effect to primary buttons */
  .btn-primary,
  .s-button-primary {
    box-shadow: 0 0 15px rgba(111, 76, 255, 0.5);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 0 20px rgba(111, 76, 255, 0.8);
    }
  }

  /* Add subtle glow effect to product cards */
  .product-entry {
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;

    .product-title {
      font-weight: bold;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(111, 76, 255, 0.2);
    }
  }

  /* Navigation styling */
  #mainnav {
    background-color: rgba(10, 10, 15, 0.8) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(111, 76, 255, 0.3);

    .menu-item > a {
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        color: var(--color-primary);
        text-shadow: 0 0 10px var(--color-primary);
      }
    }
  }

  /* Footer styling */
  footer {
    background-color: rgba(10, 10, 15, 0.9) !important;
    border-top: 1px solid rgba(111, 76, 255, 0.3);
  }

  /* Cart and checkout styling */
  .cart-item,
  .checkout-block {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    border-radius: 8px;
    border: 1px solid rgba(111, 76, 255, 0.2);
  }
}