{#
| Variable         | Type    | Description                     |
|------------------|---------|---------------------------------|
| cart             | object  |                                 |
| cart.items_count | int     |                                 |
| cart.total       | string  | Formatted total ex: "١٠٠ ر.س"   |
#}
{% set important_links = theme.settings.get('important_links') %}
<header class="store-header">
  {# Top Nav #}
  <div class="top-navbar">
    <div class="container flex justify-between">
      <div class="flex-1 flex items-center gap-2">
          {# Footer Menu #}
          {% if important_links %}
            <salla-menu source="footer" topnav></salla-menu>
          {% endif %}

          {# Language & Currency Component #}
          <div class="header-buttons">
              {% if store.settings.is_multilingual or store.settings.currencies_enabled %}
                  <button type="button" onclick="salla.event.dispatch('localization::open')" class="btn--rounded-gray basis-0">
                      <span class="flag iti__flag iti__{{ user.language.country_code }} rounded-sm"></span>
                      <span class="mx-1.5">|</span> <span>{{ currency.symbol }}</span>
                  </button>
                  <salla-localization-modal></salla-localization-modal>
              {% endif %}
          </div>

          {# Scopes | Branches #}
          {% if store.scope %}
              <button class="btn--rounded-gray"
                      onclick="salla.event.dispatch('scopes::open', {'mode': 'default'})">
                  <i class="sicon-location rtl:ml-1 ltr:mr-1"></i> {{ store.scope.name }}
              </button>
          {% endif %}
      </div>

      <salla-contacts is-header></salla-contacts>
    </div>
  </div>

  {# Search bar - Moved outside of the navbar for better z-index handling #}
  <div class="container">
    <div class="header-search flex-1">
      <div id="search-container" class="gaming-search-container">
        <category-search class="gaming-search"></category-search>
        <noscript>
          <salla-search inline oval height="36"></salla-search>
        </noscript>
        <div class="gaming-search-glow"></div>
      </div>
    </div>
  </div>

  {# Main Nav #}
  <div id="mainnav" class="main-nav-container shadow-default bg-white">
      <div class="inner bg-inherit">
          <div class="container">
              <div class="flex items-stretch justify-between relative">
                  <div class="flex items-center">
                      <a class="lg:hidden mburger mburger--collapse leading-none rtl:ml-4 ltr:mr-4" href="#mobile-menu" aria-label="Open-menu">
                        <i class="sicon-menu text-primary text-2xl"></i>
                      </a>
                      <a class="navbar-brand" href="{{ store.url }}">
                          <img fetchpriority="high" width="100%" height="100%" loading="eager" src="{{ store.logo }}" alt="{{ store.name }} logo">
                          {% if is_page('index') %}
                            <h1 class="sr-only">{{ store.name }}</h1>
                          {% else %}
                            <h2 class="sr-only">{{ store.name }}</h2>
                          {% endif %}
                      </a>
                      <custom-main-menu></custom-main-menu>
                  </div>
                  <div class="flex items-center justify-end">
                      {% if user.type=='user' %}
                        <salla-user-menu avatar-only show-header></salla-user-menu>
                      {% else %}
                          <button class="header-btn" aria-label="user-icon" onclick="salla.event.dispatch('login::open')">
                            <i class="header-btn__icon sicon-user-circle"></i>
                          </button>
                      {% endif %}
                      <salla-cart-summary class="ml-4 rtl:ml-[unset] rtl:mr-4">
                        <i slot="icon" class="header-btn__icon icon sicon-shopping-bag"></i>
                      </salla-cart-summary>
                  </div>
              </div>
          </div>
      </div>
  </div>
  
  {# Categories Filter #}
  {# <div class="categories-filter-container py-2 bg-transparent border-t border-purple-500/20">
    <div class="container">
      <div class="categories-filter-wrapper">
        <div class="gaming-glow-line"></div>
        <category-filter></category-filter>
      </div>
    </div>
  </div> #}
</header>
{% if store.scope %}
    <salla-scopes selection="{{ store.scope.display_as == 'popup' ? 'mandatory' : 'optional' }}"></salla-scopes>
{% endif %}
