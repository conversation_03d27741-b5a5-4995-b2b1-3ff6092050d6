{#
| Variable            | Type     | Description                                     |
|---------------------|----------|-------------------------------------------------|
| component.title     | string   | Section title                                   |
| component.icon      | array    | Array of icons to display                       |
| component.icon[].image | string | Icon image URL                                 |
| component.icon[].title | string | Icon title                                     |
#}
{% set component_class = component_class|default('s-block container py-10') %}
{% set title = component.title %}
{% set icon_items = component.icon %}
{% set has_slider = icon_items|length > 3 %}

<section class="gaming-gallery-section {{ component_class }}">
    <div class="gaming-gallery-container">
        <div class="gaming-gallery-title-wrapper">
            <h2 class="gaming-gallery-title">{{ title }}</h2>
            <div class="gaming-title-underline"></div>
        </div>
        
        <div class="gaming-gallery-slider {% if has_slider %}with-slider{% endif %}" id="gaming-gallery-slider">
            <div class="gaming-gallery-track">
                {% for item in icon_items %}
                    <div class="gaming-gallery-item">
                        <div class="gaming-gallery-image-wrapper">
                            <div class="gaming-gallery-image-glow"></div>
                            <div class="gaming-gallery-image-container">
                                <img src="{{ item.image }}" alt="{{ item.title }}" class="gaming-gallery-image" loading="lazy">
                            </div>
                        </div>
                        <h3 class="gaming-gallery-item-title">{{ item.title }}</h3>
                    </div>
                {% endfor %}
            </div>
            
            {% if has_slider %}
                <div class="gaming-gallery-nav">
                    <button class="gaming-gallery-prev-btn" aria-label="Previous slide">
                        <i class="sicon-chevron-left"></i>
                    </button>
                    <div class="gaming-gallery-dots">
                        {% for i in 0..((icon_items|length / 3)|round(0, 'ceil') - 1) %}
                            <button class="gaming-gallery-dot {% if loop.first %}active{% endif %}" data-index="{{ i }}" aria-label="Go to slide {{ i + 1 }}"></button>
                        {% endfor %}
                    </div>
                    <button class="gaming-gallery-next-btn" aria-label="Next slide">
                        <i class="sicon-chevron-right"></i>
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof salla.modules.gaming === 'undefined') {
            salla.modules.gaming = {};
        }
        
        if (typeof salla.modules.gaming.gallery === 'undefined') {
            import('/assets/js/partials/gaming-gallery.js')
                .then(module => {
                    salla.modules.gaming.gallery = module;
                    module.initGamingGallery();
                })
                .catch(err => console.error('Error loading gaming gallery module:', err));
        } else {
            salla.modules.gaming.gallery.initGamingGallery();
        }
    });
</script>
