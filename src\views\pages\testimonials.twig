{% extends "layouts.master" %}
{% block content %}
<div class="container">

    {# add breadcumbs container in pages to make a space in case breadcrumbs is off #}
    <nav class="breadcrumbs w-full py-5">
        <salla-breadcrumb></salla-breadcrumb>
    </nav>
    <div class="flex justify-center">
        <div class="w-full lg:w-10/12 bg-white rounded p-6 lg:p-8 mt-4 lg:mt-12">

           <div class="mb-4 sm:mb-6 flex justify-between items-center">
                <h1 class="font-bold text-2xl">{{ page.title }}</h1>
                <div class="flex items-center">
                    <label class="hidden sm:block rtl:ml-3 ltr:mr-3 whitespace-nowrap"
                        for="testimonials-filter">{{ trans('pages.categories.sorting') }}</label>
                    <select id="testimonials-filter" class="form-input pt-0 pb-1 rtl:pl-10 ltr:pr-10">
                        <option value="latest">{{ trans('pages.testimonials.sort_by_date_desc') }}</option>
                        <option value="oldest">{{ trans('pages.testimonials.sort_by_date_asc') }}</option>
                        <option value="top_rating">{{ trans('pages.testimonials.sort_by_rating_desc') }}</option>
                        <option value="bottom_rating">{{ trans('pages.testimonials.sort_by_rating_asc') }}</option>
                    </select>
                </div>
            </div>
         
            <salla-comments testimonials></salla-comments>

        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script defer src="{{ 'testimonials.js' | asset }}"></script>
{% endblock %}
